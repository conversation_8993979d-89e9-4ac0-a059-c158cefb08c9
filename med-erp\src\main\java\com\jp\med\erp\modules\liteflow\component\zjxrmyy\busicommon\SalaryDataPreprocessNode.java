package com.jp.med.erp.modules.liteflow.component.zjxrmyy.busicommon;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.feign.HrmOrgMapFeignService;
import com.jp.med.common.vo.EmpEmployeeDictVo;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * 数据预处理组件
 * 负责科室映射关系获取、人员类型转换等预处理工作
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@LiteflowComponent(value = "SalaryDataPreprocessNode", name = "工资数据预处理")
@Slf4j
public class SalaryDataPreprocessNode extends NodeComponent {

    @Autowired
    private HrmOrgMapFeignService hrmOrgMapFeignService;

    /**
     * 处理数据预处理逻辑
     */
    @Override
    public void process() throws Exception {
        log.info("开始执行数据预处理组件");
        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 2.获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);
        try {
            // 1. 预处理任务明细数据
            preprocessTaskDetails(flowContext, baseParamDtos);
            log.info("数据预处理组件执行完成");
        } catch (Exception e) {
            log.error("数据预处理组件执行失败", e);
            flowContext.markFlowFailed("数据预处理失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 预处理任务明细数据
     */
    private void preprocessTaskDetails(SalaryVcrFlowContextVo context, SalaryVcrBaseParamVo baseParamDtos) {
        if (context.getTaskDetails() == null || context.getTaskDetails().isEmpty()) {
            log.warn("任务明细数据为空，跳过预处理");
            return;
        }
        List<SalaryVcrTaskDetialVo> vcrTaskDetialVoList = new ArrayList<>();
        SalaryVcrTaskDetialVo vcrTaskDetialVo;
        String orgName, empType, deptType, econSubCode;
        // 处理每个任务明细
        for (ErpReimSalaryTaskDetailDto detail : context.getTaskDetails()) {
            // 跳过金额为0的明细
            if (detail.getReimAmt() == null || detail.getReimAmt().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            vcrTaskDetialVo = new SalaryVcrTaskDetialVo();
            BeanUtils.copyProperties(detail, vcrTaskDetialVo);

            // 科室信息
            orgName = "未知科室";
            var mapping = context.getOrgMappings().get(detail.getOrgId());
            if (mapping != null && StringUtils.isNotEmpty(mapping.getHrpOrgName())) {
                orgName = mapping.getHrpOrgName();
            }
            vcrTaskDetialVo.setOrgName(orgName);

            // 人员类型
            vcrTaskDetialVo.setEmpTypeDesc(detail.getEmpType());
            vcrTaskDetialVo.setEmpType(getSalaryEmpType(detail.getEmpType(), baseParamDtos));

            // 科室类型
            deptType = getDeptType(detail.getOrgId(), context.getOrgMappings());
            vcrTaskDetialVo.setDeptType(deptType);

            //费用科目编码
            //人力临时增加和财务临时增加特殊处理
            if(ErpConstants.TEMPORARY_ADD_SALARY.equals(detail.getReimName())){
                //是人力临时增加
                vcrTaskDetialVo.setEconSubCode(ErpConstants.MENPOWER_TEMP_ADD);
            }else if(ErpConstants.TEMPORARY_ADD_SALARY2.equals(detail.getReimName())){
                //是财务临时增加
                vcrTaskDetialVo.setEconSubCode(ErpConstants.FINAN_TEMP_ADD);
            }else {
                //其它类型
                vcrTaskDetialVo.setEconSubCode(detail.getReimType());
            }
            // 其他默认信息
            vcrTaskDetialVo.setFundNature("003"); // 默认自有资金
            vcrTaskDetialVo.setEnableYear("1"); // 默认当年

            // 设置工资类型，根据上下文工资类型获取
            vcrTaskDetialVo.setSalaryType(context.getSalaryType());

            // 根据reim_name设置对应的险种类型参数
            setInsuranceTypeByReimName(detail.getReimName(), vcrTaskDetialVo);
            vcrTaskDetialVoList.add(vcrTaskDetialVo);
        }
        context.setVcrTaskDetails(vcrTaskDetialVoList);
        log.info("任务明细数据预处理完成，共 {} 条明细", context.getTaskDetails().size());
    }

    /**
     * 根据明细数据内容，设置对应的险种类型及费用经济科目
     *
     * @param reim_name
     * @param vcrTaskDetialVo
     */
    private void setInsuranceTypeByReimName(String reim_name, SalaryVcrTaskDetialVo vcrTaskDetialVo) {
        switch (reim_name) {
            //1、个人+企业计提
            //机关事业单位基本养老保险缴费
            case ErpConstants.PENSION_INSURANCE_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_ELDERLY_CARE);
                break;
            //职工企业基本养老保险缴费
            case ErpConstants.PENSION_INSURANCE_ENTP2:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_ELDERLY_CARE);
                break;
            //职工基本医疗保险缴费
            case ErpConstants.MEDICAL_INSURANCE_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_MED);
                break;
            //职工失业保险缴
            case ErpConstants.UNEMPLOYMENT_INSURANCE_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_UN_EMP);
                break;
            //职业年金缴费
            case ErpConstants.OCCUPATION_ANNUITY_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_ANNUITY);
                break;
            //住房公积金
            case ErpConstants.HOUSING_FUND_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_HOUSING_FUND);
                break;
            //职工工伤保险缴费
            case ErpConstants.INJR_INSU_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_INJURY);
                break;
            // 工会会费支出
            case ErpConstants.PUB_FEE_ENTP:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_UNION);
                break;

            //2、工资发放+个人扣款
            // 人力临时扣款
            case ErpConstants.TEMPORARY_REDUCE_SALARY:
                vcrTaskDetialVo.setEconSubCode(ErpConstants.MENPOWER_TEMP_DEDUCT);
                break;
            // 财务临时扣款
            case ErpConstants.TEMPORARY_REDUCE_SALARY2:
                vcrTaskDetialVo.setEconSubCode(ErpConstants.FINAN_TEMP_DEDUCT);
                break;

            // 3、个人计提三险两金
            // 养老保险-个人缴纳
            case ErpConstants.PENSION_INSURANCE:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_ELDERLY_CARE);
                break;
            // 医疗保险-个人缴纳
            case ErpConstants.MEDICAL_INSURANCE:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_MED);
                break;
            // 失业保险-个人缴纳
            case ErpConstants.UNEMPLOYMENT_INSURANCE:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_UN_EMP);
                break;
            // 住房公积金-个人缴纳
            case ErpConstants.HOUSING_FUND:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_HOUSING_FUND);
                break;
            // 职业年金-个人缴纳
            case ErpConstants.OCCUPATION_ANNUITY:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_ANNUITY);
                break;
            // 工会会费-个人缴纳
            case ErpConstants.LABOR_UNION:
                vcrTaskDetialVo.setInsuranceType(ErpConstants.INSURANCE_TYPE_UNION);
                break;
            default:
                break;
        }
    }

    /**
     * 获取科室类型
     */
    public static String getDeptType(String orgId, Map<String, HrmOrgAgencyMapVo> orgMappings) {
        HrmOrgAgencyMapVo mapping = orgMappings.get(orgId);
        if (mapping != null && mapping.getYyOrgCode() != null && mapping.getYyOrgCode().length() >= 2) {
            String prefix = mapping.getYyOrgCode().substring(0, 2);
            if (Arrays.asList("01", "02", "03").contains(prefix)) {
                return MedConst.TYPE_1; // 临床医技医辅
            } else {
                return MedConst.TYPE_2; // 行政
            }
        }
        return MedConst.TYPE_1; // 默认临床
    }

    /**
     * 获取工资人员类型
     */
    public static String getSalaryEmpType(String empType, SalaryVcrBaseParamVo baseParamDtos) {
        List<EmpEmployeeDictVo> dictList = baseParamDtos.getEmpEmployeeDictVos();
        for (EmpEmployeeDictVo employeeDictVo :
                dictList) {
            if (empType.equals(employeeDictVo.getCodeLable())) {
                return employeeDictVo.getId().toString();
            }
        }
        // 18在编IDS
        return "18";
    }
}