package com.jp.med.erp.modules.liteflow.component.expense;

import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.entity.ErpReimItemDetail;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.HrmOrgMapFeignService;
import com.jp.med.common.util.DateUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购凭证生成组件
 * 专门处理采购凭证(TYPE_8)和物资采购凭证(TYPE_10)的复杂逻辑
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "PurchaseVoucherGenerateComponent", name = "采购凭证生成")
public class PurchaseVoucherGenerateComponent extends NodeComponent {

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Autowired
    private HrmOrgMapFeignService hrmOrgMapFeignService;

    @Override
    public void process() throws Exception {
        log.info("开始执行采购凭证生成节点");

        // 获取流程参数
        VoucherFlowParam param = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);

        try {
            // 从上下文获取报销明细数据
//            @SuppressWarnings("unchecked")
//            List<ErpReimDetailVo> erpReimDetailVos = (List<ErpReimDetailVo>) flowContext.getExtParams().get("erpReimDetailVos");
//
//            if (erpReimDetailVos == null || erpReimDetailVos.isEmpty()) {
//                throw new RuntimeException("未找到报销明细数据");
//            }
//
//            List<ErpReimAsstDto> insertAssts = generatePurchaseVoucherAssts(param, flowContext, erpReimDetailVos);
//
//            // 设置创建人员信息
//            insertAssts.forEach(asst -> {
//                asst.setCrter(param.getRecordPersonId());
//                asst.setCreateTime(DateUtil.getCurrentTime(null));
//                asst.setHospitalId(param.getHospitalId());
//            });
//
//            // 添加到上下文
//            flowContext.addGeneratedAssts(insertAssts);
//
//            log.info("采购凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());

        } catch (Exception e) {
            log.error("采购凭证生成失败", e);
            flowContext.markFlowFailed("采购凭证生成失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 生成采购凭证辅助项
     * 基于原有的generalPurcAsst方法逻辑
     */
    private List<ErpReimAsstDto> generatePurchaseVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                            List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("------------------start生成采购assts-------------------------");
        List<ErpReimAsstDto> assts = new ArrayList<>();
        
        // 判断付款方式 0非现金 1现金
        if (MedConst.TYPE_0.equals(param.getPayMethod())) {
            // 所有报销的pay_rcpt_id 都不能为空
            erpReimDetailVos.stream().forEach(reim -> {
                if (Objects.isNull(reim.getPayRcptId())) {
                    throw new AppException("存在未上传付款证明文件的报销");
                }
            });
            
            // 获取当前报销对应的所有pay_rcpt_id 去重
            Set<Integer> payRcptIds = erpReimDetailVos.stream()
                    .map(ErpReimDetailVo::getPayRcptId)
                    .collect(Collectors.toSet());
            
            // 判断payRcptIds 包含了所有对应的报销
            List<ErpReimDetailVo> erpReimDetailVos1 = erpVcrDetailReadMapper.queryReimDetailByPayRcptId(new ArrayList<>(payRcptIds));
            
            // 获取pay_rcpt_id 对应的所有报销
            Set<Integer> allReimIds = erpReimDetailVos1.stream()
                    .map(ErpReimDetailVo::getId)
                    .collect(Collectors.toSet());
            
            // 判断两者报销id是否相同，以此判断是否选择了当前pay_rcpt_id对应的所有报销
            if ((param.getIds().size() != allReimIds.size()) || 
                (param.getIds().size() == allReimIds.size() && !allReimIds.containsAll(param.getIds()))) {
                throw new AppException("请选择当前付款文件对应的所有报销");
            }
        }

        // 查询报销项目明细
        List<ErpReimItemDetail> erpReimItemDetails = erpVcrDetailReadMapper.queryItemDetail(param.getIds());
        
        // 查询hrp-用友科室映射
        // CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());
        // List<HrmOrgAgencyMapVo> orgAgencyMapVos = (result != null && result.getData() != null) ? result.getData() : new ArrayList<>();

        int asstNo = flowContext.getNextAsstNo();
        
        // 生成采购凭证的具体逻辑
        // 这里需要根据原有的generalPurcAsst方法来实现复杂的采购凭证生成逻辑
        
        // 示例：生成简化的采购凭证
        for (ErpReimDetailVo reimDetail : erpReimDetailVos) {
            // 生成预算借方辅助项
            ErpReimAsstDto ysCR = new ErpReimAsstDto();
            ysCR.setSupType(param.getSupType());
            ysCR.setActigSys(MedConst.TYPE_2); // 预算会计
            ysCR.setActigAmtType(MedConst.TYPE_1); // 借方
            ysCR.setActigSubCode("503020101");
            ysCR.setActigSubName("材料费");
            ysCR.setActigAmt(reimDetail.getSum());
            ysCR.setReimDetailId(reimDetail.getId());
            ysCR.setAsstNo(asstNo++);
            ysCR.setAbst("采购材料-" + reimDetail.getId());
            assts.add(ysCR);
            
            // 生成预算贷方辅助项
            ErpReimAsstDto ysDR = new ErpReimAsstDto();
            ysDR.setSupType(param.getSupType());
            ysDR.setActigSys(MedConst.TYPE_2); // 预算会计
            ysDR.setActigAmtType(MedConst.TYPE_2); // 贷方
            ysDR.setActigSubCode("602080201");
            ysDR.setActigSubName("预算应付账款");
            ysDR.setActigAmt(reimDetail.getSum());
            ysDR.setReimDetailId(reimDetail.getId());
            ysDR.setAsstNo(asstNo++);
            ysDR.setAbst("采购材料-" + reimDetail.getId());
            assts.add(ysDR);
        }
        
        // 更新上下文中的辅助项编号计数器
        flowContext.setAsstNoCounter(asstNo);
        
        log.info("------------------end生成采购assts-------------------------");
        return assts;
    }
} 