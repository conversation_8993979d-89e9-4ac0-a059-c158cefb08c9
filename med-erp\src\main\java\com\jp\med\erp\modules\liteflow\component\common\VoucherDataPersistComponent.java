package com.jp.med.erp.modules.liteflow.component.common;

import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.mapper.write.ErpVcrDetailWriteMapper;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 通用数据持久化组件
 * 负责保存凭证辅助项到数据库
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "VoucherDataPersistComponent", name = "凭证数据持久化")
public class VoucherDataPersistComponent extends NodeComponent {

    @Autowired
    private ErpVcrDetailWriteMapper erpVcrDetailWriteMapper;

    @Override
    public void process() throws Exception {
        log.info("开始执行凭证数据持久化节点");

        // 获取流程参数
        VoucherFlowParam param = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);

        try {
            List<ErpReimAsstDto> insertAssts = flowContext.getGeneratedAssts();
            
            if (insertAssts == null || insertAssts.isEmpty()) {
                log.warn("没有需要保存的凭证辅助项");
                return;
            }

            log.info("准备保存 {} 个凭证辅助项", insertAssts.size());

            // 删除旧辅助项目（如果不是采购类型）
            if (!MedConst.TYPE_8.equals(param.getType()) && !MedConst.TYPE_10.equals(param.getType())) {
                // 这里可以添加删除旧辅助项的逻辑
                // 需要根据具体的mapper方法来实现
                log.info("跳过删除旧辅助项目（需要根据具体需求实现）");
            }

            // 批量插入新的辅助项
            BatchUtil.batch("insertReimAsst", insertAssts, ErpVcrDetailWriteMapper.class);
            
            log.info("凭证数据持久化节点执行完成，成功保存 {} 个辅助项", insertAssts.size());

            // 标记流程成功
//            flowContext.markFlowSuccess();

        } catch (Exception e) {
            log.error("凭证数据持久化失败", e);
            flowContext.markFlowFailed("凭证数据持久化失败: " + e.getMessage());
            throw e;
        }
    }
} 