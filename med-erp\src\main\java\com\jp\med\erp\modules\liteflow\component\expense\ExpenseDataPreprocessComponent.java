package com.jp.med.erp.modules.liteflow.component.expense;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.ValidateUtil;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import com.jp.med.erp.modules.liteflow.service.VoucherFlowService;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.entity.ErpReimPsnDetail;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 费用报销数据查询组件
 * 负责查询报销明细等基础数据
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "ExpenseDataPreprocessComponent", name = "费用报销数据预处理")
public class ExpenseDataPreprocessComponent extends NodeComponent {

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Autowired
    VoucherFlowService voucherFlowService;

    @Override
    public void process() throws Exception {

        log.info("开始执行费用报销数据预处理节点");
        // 获取流程参数
        ExpenseVcrBaseParamVo baseParamVo = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);
        try {
            // 查询报销明细,调整为外部查询好后，组装进baseParamVo
            List<ErpReimDetailVo> erpReimDetailVos = baseParamVo.getErpReimDetailVos();

            if (erpReimDetailVos == null || erpReimDetailVos.isEmpty()) {
                throw new RuntimeException("未获取到报销明细数据，报销IDs: " + baseParamVo.getIds());
            }
            log.info("获取到报销明细数量: {}", erpReimDetailVos.size());
            List<ExpenseVcrTaskDetialVo> vcrTaskDetialVoList = new ArrayList<>();
            ExpenseVcrTaskDetialVo vcrTaskDetialVo;
            // 出差人员记录
            List<ErpReimPsnDetail> erpReimPsnDetails;
            // 付款证明文件
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos;
            // 冲抵报销原凭证明细
            List<ErpReimAsstVo> erpReimAsstVos;
            String orgName, deptType;
            // 处理每个任务明细
            for (ErpReimDetailVo detail : erpReimDetailVos) {
                // 跳过金额为0的明细
                if (detail.getSum() == null || detail.getSum().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                vcrTaskDetialVo = new ExpenseVcrTaskDetialVo();
                BeanUtils.copyProperties(detail, vcrTaskDetialVo);

                // 出差人员信息
                erpReimPsnDetails = erpVcrDetailReadMapper.queryPsnDetail(Arrays.asList(detail.getId()));
                vcrTaskDetialVo.setErpReimPsnDetails(ValidateUtil.isNotEmpty(erpReimPsnDetails) ? erpReimPsnDetails : new ArrayList<>());

                // 付款证明文件，对应报销付款证明信息
                erpReimPayReceiptVos = voucherFlowService.getPayReceiptInfos(flowContext.getSupType(), detail.getId());
                vcrTaskDetialVo.setErpReimPayReceiptVos(ValidateUtil.isNotEmpty(erpReimPayReceiptVos) ? erpReimPayReceiptVos : new ArrayList<>());

                // 科室信息
                orgName = "未知科室";
                var mapping = baseParamVo.getOrgMappings().get(detail.getAppyerDept());
                if (mapping != null && StringUtils.isNotEmpty(mapping.getHrpOrgName())) {
                    orgName = mapping.getHrpOrgName();
                }
                vcrTaskDetialVo.setOrgName(orgName);

                // 科室类型
                deptType = getDeptType(detail.getAppyerDept(), baseParamVo.getOrgMappings());
                vcrTaskDetialVo.setDeptType(deptType);

                // 设置报销名称，根据上下文工资类型获取 根据reimType获取对应的报销名称
                vcrTaskDetialVo.setReimType(baseParamVo.getReimType());

                // 设置报销名称，根据上下文工资类型获取 根据reimType获取对应的报销名称
                vcrTaskDetialVo.setReimName(baseParamVo.getReimNameType());

                // 设置报销金额
                vcrTaskDetialVo.setReimAmt(detail.getSum());

                // 其他默认信息
                vcrTaskDetialVo.setFundNature("003"); // 默认自有资金
                vcrTaskDetialVo.setEnableYear("1"); // 默认当年

                //设置是否冲抵借款
                vcrTaskDetialVo.setIsLoan(detail.getIsLoan());

                //设置冲抵借款金额
                vcrTaskDetialVo.setLoanAmt(detail.getLoanAmt());
                //设置冲抵借款报销id
                vcrTaskDetialVo.setLoanReimId(detail.getLoanReimId());
                //设置支付类型 1现金支付，空或者0为非现金支付
                vcrTaskDetialVo.setPayMethod(detail.getPayMethod());

                // 如果冲抵借款原报销id不为空，则查询原凭证明细
                if (!ValidateUtil.isEmpty(detail.getLoanReimId())) {
                    ErpReimAsstDto param = new ErpReimAsstDto();
                    param.setReimDetailId(detail.getLoanReimId());
                    param.setSupType(MedConst.TYPE_1);
                    erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(param);
                    vcrTaskDetialVo.setErpReimAsstVos(ValidateUtil.isNotEmpty(erpReimAsstVos) ? erpReimAsstVos : new ArrayList<>());
                }

                // 设置申请时间
                vcrTaskDetialVo.setAppyerTime(detail.getAppyerTime());

                // 设置报销明细id
                vcrTaskDetialVo.setTaskId(detail.getId());

                // 根据reim_name设置对应的险种类型参数
                vcrTaskDetialVoList.add(vcrTaskDetialVo);
            }
            // 设置报销任务明细数据
            flowContext.setVcrTaskDetails(vcrTaskDetialVoList);

            log.info("费用报销数据查询节点执行完成");

        } catch (Exception e) {
            log.error("费用报销数据查询失败", e);
            flowContext.markFlowFailed("费用报销数据查询失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 获取科室类型
     */
    public static String getDeptType(String orgId, Map<String, HrmOrgAgencyMapVo> orgMappings) {
        HrmOrgAgencyMapVo mapping = orgMappings.get(orgId);
        if (mapping != null && mapping.getYyOrgCode() != null && mapping.getYyOrgCode().length() >= 2) {
            String prefix = mapping.getYyOrgCode().substring(0, 2);
            if (Arrays.asList("01", "02", "03").contains(prefix)) {
                return MedConst.TYPE_1; // 临床医技医辅
            } else {
                return MedConst.TYPE_2; // 行政
            }
        }
        return MedConst.TYPE_1; // 默认临床
    }

}