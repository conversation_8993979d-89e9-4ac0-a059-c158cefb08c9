package com.jp.med.erp.modules.liteflow.component.expense;

import cn.hutool.core.collection.CollectionUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.service.VoucherFlowService;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.*;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.jp.med.erp.modules.vcrGen.entity.ErpReimPsnDetail;
import com.jp.med.erp.modules.vcrGen.entity.FileRecordEntity;
import com.jp.med.erp.modules.vcrGen.enums.FeeNameEnum;
import com.jp.med.erp.modules.vcrGen.vo.ErpDrugReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 差旅费用报销凭证生成
 * 负责根据不同费用类型生成对应的凭证辅助项
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "TravelVcrGenerateComponent", name = "差旅费用报销凭证生成")
public class TravelVcrGenerateComponent extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Resource
    VoucherFlowService voucherFlowService;

    @Override
    public void process() throws Exception {
        log.info("开始执行费用报销凭证生成节点");
        // 获取流程参数
        ExpenseVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);
        try {
            // 从上下文获取报销明细数据
            List<ExpenseVcrTaskDetialVo> erpReimDetailVos = flowContext.getVcrTaskDetails();
            if (erpReimDetailVos == null || erpReimDetailVos.isEmpty()) {
                throw new RuntimeException("未找到报销明细数据");
            }
            List<ErpReimAsstDto> insertAssts = new ArrayList<>();
            // 2. 遍历明细，基于模版引擎生成辅助项
            //出差人员信息
            List<ErpReimPsnDetail> erpReimPsnDetails;
            // 出差人员科室分组
            Map<String, List<ErpReimPsnDetail>> psnDetailGroup;
            // 付款证明文件
            List<ErpReimPayReceiptVo> erpReimPayReceiptVos;
            // 临时虚拟凭证对象
            ExpenseVcrTaskDetialVo virtualDetailVo;
            for (ExpenseVcrTaskDetialVo detail : erpReimDetailVos) {
                //查询付款证明文件
                erpReimPayReceiptVos = voucherFlowService.getPayReceiptInfos(flowContext.getSupType(), detail.getId());
                // 获取出差人员信息
                erpReimPsnDetails = detail.getErpReimPsnDetails();
                // 获取科室人员分组
                psnDetailGroup = erpReimPsnDetails.stream().
                        filter(psnItem -> psnItem.getReimDetailId().equals(detail.getId())).collect(Collectors.groupingBy(ErpReimPsnDetail::getDept));
                //随行人员名字
                String psnNames = erpReimPsnDetails.stream()
                        .limit(8)
                        .map(psnItem -> psnItem.getTripPsnName())
                        .collect(Collectors.joining("、"));
                //摘要
                String abs = voucherFlowService.getAbs(erpReimPayReceiptVos, StringUtils.substring(detail.getAppyerTime(), 0, 10), psnNames, FeeNameEnum.getByType(detail.getReimType()).getFeeName());
                for (String key : psnDetailGroup.keySet()) {
                    List<ErpReimPsnDetail> psnDetails = psnDetailGroup.get(key);
                    BigDecimal groupSum = psnDetails.stream().map(ErpReimPsnDetail::getReimAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 构建科室借方虚拟数据
                    virtualDetailVo = voucherTemplateEngine.createVirtualDetail(ErpConstants.BUSIVOUCHERTYPE_TRAVEL_REIM_LEND, groupSum, abs, detail.getReimType());
                    virtualDetailVo.setOrgId(key);
                    virtualDetailVo.setOrgName(psnDetails.get(0).getDeptName());
                    insertAssts.addAll(generateAssts(baseParamDtos, virtualDetailVo, flowContext));
                    //无付款证明文件的凭证：如果非冲抵借款且支付方式为现金支付或者复明工程，则没有付款证明文件
                    if (StringUtils.equals(detail.getIsLoan(), MedConst.TYPE_0) && !StringUtils.equals(detail.getPayMethod(), MedConst.TYPE_0)) {
                        // 构建借方虚拟数据 ，现金支付模式
                        virtualDetailVo = voucherTemplateEngine.createVirtualDetail(ErpConstants.BUSIVOUCHERTYPE_TRAVEL_REIM_CASH, groupSum, abs, detail.getReimType());
                        virtualDetailVo.setOrgId(key);
                        virtualDetailVo.setOrgName(psnDetails.get(0).getDeptName());
                        insertAssts.addAll(generateAssts(baseParamDtos, virtualDetailVo, flowContext));
                    }
                }

                // 生成冲抵借款的贷方虚拟数据
                if (StringUtils.equals(detail.getIsLoan(), MedConst.TYPE_1)) {
                    //获取冲抵借款报销凭证
                    List<ErpReimAsstVo> erpReimAsstVos = detail.getErpReimAsstVos();
                    if (CollectionUtil.isEmpty(erpReimAsstVos)) {
                        log.error("冲抵借款报销未生成凭证,借款报销id：{}", detail.getLoanReimId());
                        throw new AppException(String.format("冲抵借款报销未生成凭证,借款报销id：%s", detail.getLoanReimId()));
                    }
                    //获取借款报销财务借方数据，作为当前凭证的财务贷方数据
                    List<ErpReimAsstVo> loanAsstCrs = erpReimAsstVos.stream().filter(item -> StringUtils.equals(item.getActigSys(), MedConst.TYPE_1) && StringUtils.equals(item.getActigAmtType(), MedConst.TYPE_1)).collect(Collectors.toList());
                    //修改对应借贷方和报销id
                    for (int k = 0; k < loanAsstCrs.size(); k++) {
                        ErpReimAsstVo loanReimVo = loanAsstCrs.get(k);
                        ErpReimAsstDto dr = new ErpReimAsstDto();
                        BeanUtils.copyProperties(loanReimVo, dr);
                        dr.setActigSys(MedConst.TYPE_1);
                        dr.setActigAmtType(MedConst.TYPE_2);
                        dr.setActigAmt(detail.getLoanAmt());
                        dr.setReimDetailId(detail.getId());
                        dr.setAsstNo(flowContext.getNextAsstNo());
                        insertAssts.add(dr);
                    }
                }

                // 非现金支付&复明工程，根据付款凭证进行正常的贷方凭证
                if ((StringUtils.isEmpty(detail.getPayMethod()) || StringUtils.equals(detail.getPayMethod(), MedConst.TYPE_0))) {
                    //如果非冲抵借款且支付为现金支付，不通过付款证明文件生成财务贷方 (如果为空，则默认为非现金支付)
                    for (ErpReimPayReceiptVo vo : erpReimPayReceiptVos) {
                        String reimAbs = vo.getPayDate() + "付" + psnNames + FeeNameEnum.getByType(detail.getReimType()).getFeeName();
                        //生成贷方辅助项
                        virtualDetailVo = voucherTemplateEngine.createVirtualDetail(ErpConstants.BUSIVOUCHERTYPE_TRAVEL_REIM_NORMAL, vo.getPayAmt(), reimAbs, detail.getReimType());
                        insertAssts.addAll(generateAssts(baseParamDtos, virtualDetailVo, flowContext));
                    }
                }
            }

            // 设置创建人员信息
            insertAssts.forEach(asst -> {
                asst.setCrter(baseParamDtos.getCrter());
                asst.setCreateTime(DateUtil.getCurrentTime(null));
                asst.setHospitalId(baseParamDtos.getHospitalId());
            });

            // 添加到上下文
            flowContext.addGeneratedAssts(insertAssts);

            log.info("差旅费用报销凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());

        } catch (Exception e) {
            log.error("差旅费用凭证生成失败", e);
            flowContext.markFlowFailed("差旅费用报销凭证生成失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 基于数据进行凭证模型匹配，生成凭证并返回
     *
     * @param vcrFlowBaseVo
     * @param flowContext
     * @return
     */
    private List<ErpReimAsstDto> generateAssts(ExpenseVcrBaseParamVo baseParamDtos, VcrFlowDetailBaseVo vcrFlowBaseVo, VcrFlowContextBaseVo flowContext) {
        // 1. 获取凭证模版列表（按业务凭证类型分组）
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
                .getOrDefault(vcrFlowBaseVo.getBusiVoucherType(), new ArrayList<>());

        if (templates == null || templates.isEmpty()) {
            log.warn("未找到差旅费用报销凭证模版配置");
            throw new RuntimeException("未找到费用报销凭证模版配置");
        }

        log.info("找到费用报销凭证模版数量: {}", templates.size());

        Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, vcrFlowBaseVo);
        if (matchedTemplate.isPresent()) {
            List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                    matchedTemplate.get(), vcrFlowBaseVo, flowContext);
            log.debug("差旅费用报销 {} 应用模版生成 {} 个辅助项", vcrFlowBaseVo.getReimName(), detailAssts.size());
            return detailAssts;
        } else {
            return new ArrayList<>();
        }

    }

}