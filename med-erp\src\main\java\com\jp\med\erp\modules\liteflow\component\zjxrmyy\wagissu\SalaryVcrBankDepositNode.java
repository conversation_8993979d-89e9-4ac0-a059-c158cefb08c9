package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.feign.hrm.HrmEmployeeSalaryFeignService;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 银行存款凭证生成节点
 * 处理银行存款相关的凭证生成，按原业务逻辑实现完整的工资总额和银行存款计算
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrBankDepositNode", name = "银行存款凭证生成")
public class SalaryVcrBankDepositNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Autowired
    private HrmEmployeeSalaryFeignService hrmEmployeeSalaryFeignService;

    @Override
    public void process() throws Exception {
        log.info("开始执行银行存款凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        // 获取个人代扣任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
                .stream()
                .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.INDIVDUAL_REDUCE))
                .collect(Collectors.toList());

        if (taskDetails.isEmpty()) {
            log.info("没有个人代扣任务明细，跳过银行存款处理");
            return;
        }

        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理银行存款
            insertAssts.addAll(processBankDeposit(baseParamDtos, flowContext, taskDetails));

        } catch (Exception e) {
            log.error("银行存款凭证生成失败", e);
            flowContext.markFlowFailed("银行存款凭证生成失败: " + e.getMessage());
            throw e;
        }

        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);

        log.info("银行存款凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理银行存款
     */
    private List<ErpReimAsstDto> processBankDeposit(SalaryVcrBaseParamVo baseParamDtos,
                                                    SalaryVcrFlowContextVo flowContext,
                                                    List<SalaryVcrTaskDetialVo> taskDetails) {
        log.info("开始处理银行存款");

        List<ErpReimAsstDto> result = new ArrayList<>();
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
                .getOrDefault(ErpConstants.BUSIVOUCHERTYPE_SALARY_PAYROLL, new ArrayList<>());

        if (templates.isEmpty()) {
            log.warn("未找到银行存款凭证模版配置");
            return result;
        }

        // 按原业务逻辑计算银行存款金额
        BigDecimal bankDepositAmount = calculateBankDepositAmount(baseParamDtos, flowContext, taskDetails);

        if (bankDepositAmount.compareTo(BigDecimal.ZERO) > 0) {
            SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(ErpConstants.BANK_DEPOSIT,
                    bankDepositAmount, "银行存款");

            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
            if (matchedTemplate.isPresent()) {
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), virtualDetail, flowContext);
                result.addAll(detailAssts);
                log.debug("银行存款应用模版生成 {} 个辅助项", detailAssts.size());
            }
        }

        log.info("银行存款处理完成，生成 {} 个辅助项，银行存款金额: {}", result.size(), bankDepositAmount);
        return result;
    }

    /**
     * 按原业务逻辑计算银行存款金额
     * 银行存款 = 工资总额 - 累计扣款
     * 工资总额 = 基本工资 + 国家统一规定的津贴补贴 + 规范津贴补贴 + 特殊往来账 + 冲账
     */
    private BigDecimal calculateBankDepositAmount(SalaryVcrBaseParamVo baseParamDtos,
                                                  SalaryVcrFlowContextVo flowContext,
                                                  List<SalaryVcrTaskDetialVo> taskDetails) {
        log.info("开始计算银行存款金额");

        // 1. 查询工资计提辅助项，获取应付职工薪酬科目金额
        //获取计提工资对应任务id
        Integer accrueTaskId = baseParamDtos.getSalaryTaskMap().get(ErpConstants.SALARY);
        ErpReimAsstDto asstParam = new ErpReimAsstDto();
        asstParam.setSupType(MedConst.TYPE_3);
        asstParam.setReimDetailId(accrueTaskId);
        List<ErpReimAsstVo> erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(asstParam);
        if (erpReimAsstVos.isEmpty()) {
            log.warn("未找到工资计提辅助项数据，无法计算银行存款");
            return BigDecimal.ZERO;
        }

        // 2. 计算工资总额各部分
        BigDecimal salaryTotal = BigDecimal.ZERO;

        // 应付职工薪酬
        List<SalaryVcrTaskDetialVo> payrollPayableDetailList = flowContext.getPayrollPayableDetailList();
        // 2.1、应付职工薪酬
        BigDecimal payrollPayableTotal = payrollPayableDetailList.stream()
                .map(SalaryVcrTaskDetialVo::getReimAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        salaryTotal = salaryTotal.add(payrollPayableTotal);

        // 2.2 特殊往来账
        List<SalaryVcrTaskDetialVo> specialAccountsDetailList = flowContext.getSpecialAccountsDetailList();
        BigDecimal specialAccountsTotal = specialAccountsDetailList.stream()
                .map(SalaryVcrTaskDetialVo::getReimAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        salaryTotal = salaryTotal.add(specialAccountsTotal);

        // 2.3 冲账（负数金额）
        List<SalaryVcrTaskDetialVo> strikeBalanceDetailList = flowContext.getStrikeBalanceDetailList();
        BigDecimal offsetAccountsTotal = strikeBalanceDetailList.stream()
                .map(SalaryVcrTaskDetialVo::getReimAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        salaryTotal = salaryTotal.add(offsetAccountsTotal);

        // 3. 计算累计扣款金额
        List<SalaryVcrTaskDetialVo> accumulatedDeductionsDetailList = flowContext.getAccumulatedDeductionsDetailList();
        BigDecimal totalDeductions = accumulatedDeductionsDetailList.stream()
                .map(SalaryVcrTaskDetialVo::getReimAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 4. 银行存款 = 工资总额 - 累计扣款
        BigDecimal bankDepositAmount = salaryTotal.subtract(totalDeductions);

        log.info("银行存款计算明细: 应付职工薪酬={}, 特殊往来账={}, 冲账={}, 工资总额={}, 累计扣款={}, 银行存款={}",
                payrollPayableTotal, specialAccountsTotal, offsetAccountsTotal,
                salaryTotal, totalDeductions, bankDepositAmount);

        return bankDepositAmount;
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        return detail;
    }
} 