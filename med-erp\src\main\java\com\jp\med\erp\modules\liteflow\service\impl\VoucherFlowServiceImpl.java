package com.jp.med.erp.modules.liteflow.service.impl;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.ValidateUtil;
import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.service.VoucherFlowService;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.erp.modules.vcrGen.entity.Certificate;
import com.jp.med.erp.modules.vcrGen.entity.FileRecordEntity;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpReimPayReceiptReadMapper;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.vo.ErpDrugReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import com.jp.med.erp.modules.vcrGen.vo.ExpenseFlowParam;
import com.yomahub.liteflow.core.FlowExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 通用凭证流程服务实现
 * 基于LiteFlow实现各类凭证生成
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class VoucherFlowServiceImpl implements VoucherFlowService {

    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    ErpReimPayReceiptReadMapper erpReimPayReceiptReadMapper;

    @Resource
    ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    /**
     * 生成费用报销相关凭证
     *
     * @param param 凭证流程参数
     * @return
     */
    @Override
    public List<ErpReimAsstDto> generateVoucherAssts(ExpenseFlowParam param) {
        log.info("开始生成凭证，业务分类: {}, 凭证类型: {}, 医疗机构: {}",
                param.getSupType(), param.getType(), param.getHospitalId());

        // 构建LiteFlow流程参数，查询基础数据在构建参数内
        ExpenseVcrBaseParamVo vcrBaseParamVo = buildVoucherFlowParam(param);
        // 根据业务分类路由到具体的生成方法
        switch (param.getSupType()) {
            case "1": // 费用报销
                return generateExpenseVoucherAssts(vcrBaseParamVo);
            case "2": // 药品
                return generateDrugVoucherAssts(vcrBaseParamVo);
            case "4": // 折旧
                return generateDepreciationVoucherAssts(vcrBaseParamVo);
            default:
                throw new AppException("不支持的凭证业务分类: " + vcrBaseParamVo.getSupType());
        }
    }

    /**
     * 构建LiteFlow流程参数
     */
    private ExpenseVcrBaseParamVo buildVoucherFlowParam(ExpenseFlowParam dto) {
        ExpenseVcrBaseParamVo param = new ExpenseVcrBaseParamVo();
        List<ErpReimDetailVo> erpReimDetailVos = new ArrayList<>();
        //查询明细
        switch (dto.getType()) {
            case "1":
                // 差旅报销
                erpReimDetailVos = erpVcrDetailReadMapper.queryReimDetailList(dto.getIds());
                break;
            case "2":
                // 培训报销
                break;
            case "3":
                // 其他费用
                break;
            case "4":
                // 分摊费用
                break;
            case "6":
                // 合同
                break;
            case "8":
                // 采购凭证
                break;
            case "10":
                // 物资采购
                break;
            case "11":
                // 其他费用(无发票)
                break;
            case "12":
                // 往来支付
                break;
            case "13":
                // 借款
                break;
            default:
                throw new RuntimeException("不支持的费用凭证类型: " + dto.getType());
        }
        param.setErpReimDetailVos(erpReimDetailVos);
        param.setHospitalId(dto.getHospitalId());
        param.setSupType(dto.getSupType());
        param.setReimType(dto.getType());
        // 设置业务凭证类型
        param.setBusiVoucherType(param.getReimNameType());
        param.setIds(dto.getIds());
        param.setCrter(dto.getCrter());
        param.setPayMethod(dto.getPayMethod());
        return param;
    }

    public List<ErpReimAsstDto> generateExpenseVoucherAssts(ExpenseVcrBaseParamVo param) {
        log.info("开始生成费用报销凭证，凭证类型: {}, 报销数量: {}",
                param.getReimType(), param.getIds() != null ? param.getIds().size() : 0);
        try {
            // 确定业务凭证类型
            String busiVoucherType = param.getBusiVoucherType();
            log.info("确定费用报销业务凭证类型: {}", busiVoucherType);

            // 构建流程上下文
            ExpenseVcrFlowContextVo flowContext = buildExpenseFlowContext(param);

            // 确定执行的流程链ID
            String chainId = determineChainId(param.getHospitalId(), busiVoucherType);

            // 执行流程链
            flowExecutor.execute2Resp(chainId, param, flowContext);

            log.info("费用报销凭证生成完成，生成凭证数量: {}", flowContext.getGeneratedAssts().size());
            return flowContext.getGeneratedAssts();

        } catch (Exception e) {
            log.error("费用报销凭证生成失败", e);
            throw new AppException("费用报销凭证生成失败: " + e.getMessage());
        }
    }

    public List<ErpReimAsstDto> generateDrugVoucherAssts(ExpenseVcrBaseParamVo param) {
        log.info("开始生成药品报销凭证，报销数量: {}",
                param.getIds() != null ? param.getIds().size() : 0);

        try {
            // 确定业务凭证类型
            String busiVoucherType = param.getBusiVoucherType();
            log.info("确定药品报销业务凭证类型: {}", busiVoucherType);

            // 构建流程上下文
            ExpenseVcrFlowContextVo flowContext = buildDrugFlowContext(param);

            // 确定执行的流程链ID
            String chainId = determineChainId("", busiVoucherType);

            // 执行流程链
            flowExecutor.execute2Resp(chainId, param, flowContext);

            log.info("药品报销凭证生成完成，生成凭证数量: {}", flowContext.getGeneratedAssts().size());
            return flowContext.getGeneratedAssts();

        } catch (Exception e) {
            log.error("药品报销凭证生成失败", e);
            throw new AppException("药品报销凭证生成失败: " + e.getMessage());
        }
    }

    public List<ErpReimAsstDto> generateDepreciationVoucherAssts(ExpenseVcrBaseParamVo param) {
        log.info("开始生成折旧凭证，任务ID: {}", param.getIds());

        try {
            // 确定业务凭证类型
            String busiVoucherType = param.getBusiVoucherType();
            log.info("确定折旧业务凭证类型: {}", busiVoucherType);

            // 构建流程上下文
            ExpenseVcrFlowContextVo flowContext = buildDepreciationFlowContext(param);

            // 确定执行的流程链ID
            String chainId = determineChainId("", busiVoucherType);

            // 执行流程链
            flowExecutor.execute2Resp(chainId, param, flowContext);

            log.info("折旧凭证生成完成，生成凭证数量: {}", flowContext.getGeneratedAssts().size());
            return flowContext.getGeneratedAssts();

        } catch (Exception e) {
            log.error("折旧凭证生成失败", e);
            throw new AppException("折旧凭证生成失败: " + e.getMessage());
        }
    }

    /**
     * 构建费用报销流程上下文
     */
    private ExpenseVcrFlowContextVo buildExpenseFlowContext(ExpenseVcrBaseParamVo param) {
        ExpenseVcrFlowContextVo context = new ExpenseVcrFlowContextVo();
        context.setHospitalId(param.getHospitalId());
        context.setSupType(param.getSupType());
        context.setBusiVoucherType(param.getBusiVoucherType());
        context.setReimIds(param.getIds());
        context.setPayMethod(param.getPayMethod());
        context.setStartTime(System.currentTimeMillis());
        context.setStatus(ExpenseVcrFlowContextVo.FlowExecutionStatus.RUNNING);
        return context;
    }

    /**
     * 构建药品报销流程上下文
     */
    private ExpenseVcrFlowContextVo buildDrugFlowContext(ExpenseVcrBaseParamVo param) {
        ExpenseVcrFlowContextVo context = new ExpenseVcrFlowContextVo();
//        context.setHospitalId(param.getHospitalId());
        context.setSupType(param.getSupType());
//        context.setVoucherType(param.getType());
        context.setBusiVoucherType(param.getBusiVoucherType());
        context.setReimIds(param.getIds());
//        context.setRecordPersonId(param.getRecordPersonId());
//        context.setCreator(param.getCreator());
        context.setStartTime(System.currentTimeMillis());
        context.setStatus(ExpenseVcrFlowContextVo.FlowExecutionStatus.RUNNING);
        return context;
    }

    /**
     * 构建折旧流程上下文
     */
    private ExpenseVcrFlowContextVo buildDepreciationFlowContext(ExpenseVcrBaseParamVo param) {
        ExpenseVcrFlowContextVo context = new ExpenseVcrFlowContextVo();
//        context.setHospitalId(param.getHospitalId());
        context.setSupType(param.getSupType());
//        context.setVoucherType(param.getType());
        context.setBusiVoucherType(param.getBusiVoucherType());
        context.setReimIds(param.getIds());
//        context.setRecordPersonId(param.getRecordPersonId());
//        context.setCreator(param.getCreator());
        context.setStartTime(System.currentTimeMillis());
        context.setStatus(ExpenseVcrFlowContextVo.FlowExecutionStatus.RUNNING);
        return context;
    }

    /**
     * 确定流程链ID
     * 根据医疗机构编码和业务凭证类型确定具体的流程链
     */
    private String determineChainId(String hospitalId, String busiVoucherType) {
        // 流程链命名规则: SalaryVcrAccrueWagChain+hospitalId
        String busiVcrChainPfx = ErpConstants.busiVouchervCoverChainMap.get(busiVoucherType);
        if (ValidateUtil.isEmpty(busiVcrChainPfx)) {
            throw new AppException("无效的业务凭证类型: " + busiVoucherType);
        }
        String specificChainId = busiVcrChainPfx + hospitalId;
        log.debug("使用业务流程链: {}", specificChainId);
        return specificChainId;
    }

    /**
     * 获取辅助项摘要信息
     *
     * @param feeName 报销类型名称
     * @return
     */
    @Override
    public String getAbs(List<ErpReimPayReceiptVo> receiptVos, String date, String applyer, String feeName) {
        //如果是差旅、培训报销，摘要为 日期+随行人员+费用名称，否则为 日期+申请人+费用名称
        StringBuilder sb = new StringBuilder();
        if (receiptVos.isEmpty()) {
            return sb.append(date).append("付").append(applyer).append(feeName).toString();
        }
        ErpReimPayReceiptVo vo = receiptVos.get(0);
        return sb.append(vo.getPayDate()).append("付").append(applyer).append(feeName).toString();
    }

    /**
     * 获取付款文件明细信息
     *
     * @param supType
     * @param reimId
     * @return
     */
    @Override
    public List<ErpReimPayReceiptVo> getPayReceiptInfos(String supType, Integer reimId) {
        //查询对应报销付款证明信息
        List<ErpReimPayReceiptVo> erpReimPayReceiptVos = erpReimPayReceiptReadMapper.queryListById(supType, reimId);
        //查询报销付款证明文件
        List<FileRecordEntity> fileRecordEntities = new ArrayList<>();
        if (StringUtils.equals(supType, MedConst.TYPE_1)) {
            //费用报销
            fileRecordEntities = erpVcrDetailReadMapper.queryFileRecord(Arrays.asList(reimId));
        } else {
            //药品报销
            List<ErpDrugReimDetailVo> drugVos = erpVcrDetailReadMapper.queryDrugReimDetailVo(Arrays.asList(reimId));
            List<String> attCodes = drugVos.stream().map(ErpDrugReimDetailVo::getAttCode).collect(Collectors.toList());
            fileRecordEntities = erpVcrDetailReadMapper.queryFileRecordByCode(attCodes);
        }

        List<FileRecordEntity> collect = fileRecordEntities
                .stream()
                .filter(item -> StringUtils.equals(item.getType(), MedConst.TYPE_2))
                .collect(Collectors.toList());
        if (erpReimPayReceiptVos.size() != collect.size()) {
            //数量不一致
            throw new AppException("存在未识别付款证明文件的报销");
        }
        erpReimPayReceiptVos.stream().forEach(item -> {
            if (Objects.isNull(item.getPayAmt()) || StringUtils.isEmpty(item.getPayDate())) {
                throw new AppException("存在金额或者日期为空的付款文件识别信息，请修正");
            }
        });
        return erpReimPayReceiptVos;
    }

} 