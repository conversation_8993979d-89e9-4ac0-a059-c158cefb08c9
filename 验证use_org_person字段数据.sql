-- 🔍 验证 use_org_person 字段数据查询
-- 请在数据库中执行以下查询来检查数据

-- ==============================================
-- 1. 检查合同336的use_org_person字段
-- ==============================================
SELECT 
    id, 
    ct_name, 
    use_org_person,
    CASE 
        WHEN use_org_person IS NULL THEN 'NULL'
        WHEN use_org_person = '' THEN 'EMPTY'
        ELSE 'HAS_VALUE'
    END as field_status
FROM cms_contract 
WHERE id = 336;

-- ==============================================
-- 2. 检查合同151的use_org_person字段
-- ==============================================
SELECT 
    id, 
    ct_name, 
    use_org_person,
    CASE 
        WHEN use_org_person IS NULL THEN 'NULL'
        WHEN use_org_person = '' THEN 'EMPTY'
        ELSE 'HAS_VALUE'
    END as field_status
FROM cms_contract 
WHERE id = 151;

-- ==============================================
-- 3. 检查所有合同的use_org_person字段使用情况
-- ==============================================
SELECT 
    COUNT(*) as total_contracts,
    COUNT(use_org_person) as has_use_org_person,
    COUNT(CASE WHEN use_org_person IS NOT NULL AND use_org_person != '' THEN 1 END) as has_non_empty_use_org_person,
    ROUND(
        COUNT(CASE WHEN use_org_person IS NOT NULL AND use_org_person != '' THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as fill_rate_percentage
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0;

-- ==============================================
-- 4. 查看有use_org_person数据的合同样例
-- ==============================================
SELECT 
    id, 
    ct_name, 
    use_org_person,
    use_org,
    manage_org,
    responsible_person
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
  AND use_org_person IS NOT NULL 
  AND use_org_person != ''
ORDER BY id DESC
LIMIT 5;

-- ==============================================
-- 5. 测试关联查询 - 检查员工信息是否能正确关联
-- ==============================================
SELECT 
    co.id,
    co.ct_name,
    co.use_org_person,
    he.emp_name as use_org_person_name,
    he.is_deleted as emp_is_deleted
FROM cms_contract co
LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code
WHERE co.id IN (336, 151)
ORDER BY co.id;

-- ==============================================
-- 6. 检查hrm_employee_info表中的员工数据样例
-- ==============================================
SELECT 
    emp_code,
    emp_name,
    is_deleted,
    hospital_id
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
ORDER BY emp_code
LIMIT 10;

-- ==============================================
-- 7. 如果use_org_person字段为空，检查是否可以从其他字段推导
-- ==============================================
SELECT 
    id,
    ct_name,
    use_org,
    use_org_person,
    responsible_person,
    crter,
    appyer
FROM cms_contract 
WHERE id IN (336, 151);

-- ==============================================
-- 8. 检查是否有其他合同有完整的use_org_person数据
-- ==============================================
SELECT 
    co.id,
    co.ct_name,
    co.use_org,
    co.use_org_person,
    he.emp_name as use_org_person_name,
    co.responsible_person,
    he2.emp_name as responsible_person_name
FROM cms_contract co
LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code AND he.is_deleted = 0
LEFT JOIN hrm_employee_info he2 ON co.responsible_person = he2.emp_code AND he2.is_deleted = 0
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND co.use_org_person IS NOT NULL 
  AND co.use_org_person != ''
ORDER BY co.id DESC
LIMIT 5;
