package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.ReimTypeConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.feign.hrm.HrmEmployeeSalaryFeignService;
import com.jp.med.common.vo.EmployeeReallySalaryVo;
import com.jp.med.common.vo.HrpSalaryTask;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 冲账凭证生成节点
 * 处理冲账相关的凭证生成
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrStrikeBalanceNode", name = "冲账凭证生成")
public class SalaryVcrStrikeBalanceNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Autowired
    private HrmEmployeeSalaryFeignService hrmEmployeeSalaryFeignService;

    @Override
    public void process() throws Exception {
        log.info("开始执行冲账凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        // 获取个人代扣任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
                .stream()
                .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.INDIVDUAL_REDUCE))
                .collect(Collectors.toList());

        if (taskDetails.isEmpty()) {
            log.info("没有个人代扣任务明细，跳过冲账处理");
            return;
        }

        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理冲账
            insertAssts.addAll(processStrikeBalance(baseParamDtos, flowContext, taskDetails));

        } catch (Exception e) {
            log.error("冲账凭证生成失败", e);
            flowContext.markFlowFailed("冲账凭证生成失败: " + e.getMessage());
            throw e;
        }

        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);

        log.info("冲账凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理冲账
     */
    private List<ErpReimAsstDto> processStrikeBalance(SalaryVcrBaseParamVo baseParamDtos,
                                                      SalaryVcrFlowContextVo flowContext,
                                                      List<SalaryVcrTaskDetialVo> taskDetails) {
        log.info("开始处理冲账");

        List<ErpReimAsstDto> result = new ArrayList<>();
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
                .getOrDefault(ErpConstants.BUSIVOUCHERTYPE_STRIKE_BALANCE, new ArrayList<>());

        if (templates.isEmpty()) {
            log.warn("未找到冲账凭证模版配置");
            return result;
        }

        try {
            // 查询在职职工工资明细数据，使用baseParamDtos中的工资批次任务ID
            HrpSalaryTask salaryParam = new HrpSalaryTask();
            salaryParam.setId(baseParamDtos.getSalaryId());
            CommonResult<List<EmployeeReallySalaryVo>> result1 = hrmEmployeeSalaryFeignService.queryReallySalaryDetail(salaryParam);

            if (Objects.isNull(result1) || result1.getData() == null || result1.getData().isEmpty()) {
                log.warn("在职职工工资明细数据不存在");
                return result;
            }

            List<EmployeeReallySalaryVo> data = result1.getData();
            // 按原逻辑筛选特定科室和人员，生成冲账凭证
            result.addAll(generateStrikeBalanceEntries(baseParamDtos, flowContext, templates, data));
        } catch (Exception e) {
            log.error("处理冲账失败", e);
            throw e;
        }
        log.info("冲账处理完成，生成 {} 个辅助项", result.size());
        return result;
    }

    /**
     * 生成冲账凭证分录
     */
    private List<ErpReimAsstDto> generateStrikeBalanceEntries(SalaryVcrBaseParamVo baseParamDtos,
                                                              SalaryVcrFlowContextVo flowContext,
                                                              List<VoucherTemplateDto> templates,
                                                              List<EmployeeReallySalaryVo> data) {
        List<ErpReimAsstDto> result = new ArrayList<>();

        // 筛选特定科室和人员（按原逻辑）
        // 维修班人员中的11个人 工号：0175 0580 0601 0605 0633 0666 0669 1261 1262 1404 1406 维修班：528004
        List<String> wxCodes = Arrays.asList("0175", "0580", "0601", "0605", "0633", "0666", "0669", "1261", "1262", "1404", "1406");
        List<EmployeeReallySalaryVo> wxEmpInfo = data.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "528004") && wxCodes.contains(e.getEmpCode()))
                .collect(Collectors.toList());
        BigDecimal wxEmpInfoTotal = wxEmpInfo.stream()
                .map(EmployeeReallySalaryVo::getShouldPayTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 总务科-吴思琪 工号 1242 部门code 528001
        List<EmployeeReallySalaryVo> zwEmpInfo = data.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "528001") && StringUtils.equals(e.getEmpCode(), "1242"))
                .collect(Collectors.toList());
        BigDecimal zwEmpInfoTotal = zwEmpInfo.stream()
                .map(EmployeeReallySalaryVo::getShouldPayTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 住院收费室-罗亚婕 工号 1346 部门code 402002
        List<EmployeeReallySalaryVo> zyEmpInfo = data.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "402002") && StringUtils.equals(e.getEmpCode(), "1346"))
                .collect(Collectors.toList());
        BigDecimal zyEmpInfoTotal = zyEmpInfo.stream()
                .map(EmployeeReallySalaryVo::getShouldPayTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 单采血浆站人员 单采血浆站：405001
        List<EmployeeReallySalaryVo> dqEmpInfo = data.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "405001"))
                .collect(Collectors.toList());
        BigDecimal dqEmpInfoTotal = dqEmpInfo.stream()
                .map(EmployeeReallySalaryVo::getShouldPayTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 使用模板匹配方式生成冲账凭证（按原逻辑分别处理临床和职能科室）
        // 住院收费室 402002/住院收费室、城南院区收费室 （临床）
        if (zyEmpInfoTotal.compareTo(BigDecimal.ZERO) > 0) {
            // 创建虚拟明细用于模版匹配
            SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(
                    ErpConstants.OFFSET_ACCOUNT,
                    zyEmpInfoTotal.negate(),
                    "工资总额-冲账-住院收费室、城南院区收费室",
                    "402002",
                    "住院收费室、城南院区收费室",
                    MedConst.TYPE_1 // 临床
            );
            //设置冲账默认经济科目为岗位工资
            virtualDetail.setReimType(ReimTypeConst.GWGZ);
            virtualDetail.setEconSubCode(ReimTypeConst.GWGZ);

            // 匹配模版并生成凭证
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
            if (matchedTemplate.isPresent()) {
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), virtualDetail, flowContext);
                // 设置冲账的数据进入上下文
                flowContext.getStrikeBalanceDetailList().add(virtualDetail);
                result.addAll(detailAssts);
                log.debug("应用模版 {} 为住院收费室生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            }
        }

        // 维修班 528004/维修班 （职能）
        if (wxEmpInfoTotal.compareTo(BigDecimal.ZERO) > 0) {
            // 创建虚拟明细用于模版匹配
            SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(
                    ErpConstants.OFFSET_ACCOUNT,
                    wxEmpInfoTotal.negate(),
                    "工资总额-冲账-维修班",
                    "528004",
                    "维修班",
                    MedConst.TYPE_2 // 职能
            );
            //设置冲账默认经济科目为岗位工资
            virtualDetail.setReimType(ReimTypeConst.GWGZ);
            virtualDetail.setEconSubCode(ReimTypeConst.GWGZ);

            // 匹配模版并生成凭证
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
            if (matchedTemplate.isPresent()) {
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), virtualDetail, flowContext);
                // 设置冲账的数据进入上下文
                flowContext.getStrikeBalanceDetailList().add(virtualDetail);
                result.addAll(detailAssts);
                log.debug("应用模版 {} 为维修班生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            }
        }

        // 总务科 528001/总务科办公室 （职能）
        if (zwEmpInfoTotal.compareTo(BigDecimal.ZERO) > 0) {
            // 创建虚拟明细用于模版匹配
            SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(
                    ErpConstants.OFFSET_ACCOUNT,
                    zwEmpInfoTotal.negate(),
                    "工资总额-冲账-总务科办公室",
                    "528001",
                    "总务科办公室",
                    MedConst.TYPE_2 // 职能
            );
            //设置冲账默认经济科目为岗位工资
            virtualDetail.setReimType(ReimTypeConst.GWGZ);
            virtualDetail.setEconSubCode(ReimTypeConst.GWGZ);

            // 匹配模版并生成凭证
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
            if (matchedTemplate.isPresent()) {
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), virtualDetail, flowContext);
                // 设置冲账的数据进入上下文
                flowContext.getStrikeBalanceDetailList().add(virtualDetail);
                result.addAll(detailAssts);
                log.debug("应用模版 {} 为总务科办公室生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            }
        }

        // 单采血浆站 405001/单采血浆站 （职能）
        if (dqEmpInfoTotal.compareTo(BigDecimal.ZERO) > 0) {
            // 创建虚拟明细用于模版匹配
            SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(
                    ErpConstants.OFFSET_ACCOUNT,
                    dqEmpInfoTotal.negate(),
                    "工资总额-冲账-单采血浆站",
                    "405001",
                    "单采血浆站",
                    MedConst.TYPE_2 // 职能
            );
            //设置冲账默认经济科目为岗位工资
            virtualDetail.setReimType(ReimTypeConst.GWGZ);
            virtualDetail.setEconSubCode(ReimTypeConst.GWGZ);

            // 匹配模版并生成凭证
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
            if (matchedTemplate.isPresent()) {
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), virtualDetail, flowContext);
                // 设置冲账的数据进入上下文
                flowContext.getStrikeBalanceDetailList().add(virtualDetail);
                result.addAll(detailAssts);
                log.debug("应用模版 {} 为单采血浆站生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            }
        }

        return result;
    }

    /**
     * 创建虚拟明细用于模版匹配
     *
     * @param reimName    科目名称
     * @param amount      金额
     * @param description 描述
     * @param deptCode    部门代码
     * @param deptName    部门名称
     * @param deptType    部门类型（1-临床，2-职能）
     * @return 虚拟明细对象
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName,
                                                      BigDecimal amount,
                                                      String description,
                                                      String deptCode,
                                                      String deptName,
                                                      String deptType) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        detail.setOrgId(deptCode);
        detail.setOrgName(deptName);
        // 在描述中添加部门名称信息，因为没有对应的字段
        detail.setDeptType(deptType); // 使用DeptType字段存储部门类型
        return detail;
    }
} 