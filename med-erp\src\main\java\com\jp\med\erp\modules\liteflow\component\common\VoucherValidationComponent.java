package com.jp.med.erp.modules.liteflow.component.common;

import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.common.constant.MedConst;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 凭证数据验证组件
 * 负责验证凭证平衡性和完整性
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "VoucherValidationComponent", name = "凭证数据验证")
public class VoucherValidationComponent extends NodeComponent {

    @Override
    public void process() throws Exception {
        log.info("开始执行凭证数据验证节点");

        // 获取流程参数
        VoucherFlowParam param = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);

        try {
            List<ErpReimAsstDto> insertAssts = flowContext.getGeneratedAssts();
            
            if (insertAssts == null || insertAssts.isEmpty()) {
                log.warn("没有生成的凭证辅助项需要验证");
                return;
            }

            log.info("开始验证 {} 个凭证辅助项", insertAssts.size());

            // 执行各项验证
            validateVoucherBalance(insertAssts);
            validateVoucherCompleteness(insertAssts);
            validateVoucherAmounts(insertAssts);
            validateVoucherSubjects(insertAssts);

            log.info("凭证数据验证节点执行完成，所有验证通过");

        } catch (Exception e) {
            log.error("凭证数据验证失败", e);
            flowContext.markFlowFailed("凭证数据验证失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 验证凭证平衡性
     * 借方金额总计应等于贷方金额总计
     */
    private void validateVoucherBalance(List<ErpReimAsstDto> insertAssts) {
        log.info("开始验证凭证平衡性");

        // 按会计体系分组验证
        Map<String, List<ErpReimAsstDto>> groupByActigSys = insertAssts.stream()
                .collect(Collectors.groupingBy(ErpReimAsstDto::getActigSys));

        for (Map.Entry<String, List<ErpReimAsstDto>> entry : groupByActigSys.entrySet()) {
            String actigSys = entry.getKey();
            List<ErpReimAsstDto> assts = entry.getValue();

            // 计算借方总金额
            BigDecimal totalDebitAmount = assts.stream()
                    .filter(asst -> MedConst.TYPE_1.equals(asst.getActigAmtType())) // 借方
                    .map(ErpReimAsstDto::getActigAmt)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算贷方总金额
            BigDecimal totalCreditAmount = assts.stream()
                    .filter(asst -> MedConst.TYPE_2.equals(asst.getActigAmtType())) // 贷方
                    .map(ErpReimAsstDto::getActigAmt)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("会计体系 {} - 借方总额: {}, 贷方总额: {}", actigSys, totalDebitAmount, totalCreditAmount);

            // 验证借贷平衡
            if (totalDebitAmount.compareTo(totalCreditAmount) != 0) {
                String errorMsg = String.format("会计体系 %s 借贷不平衡：借方总额 %s，贷方总额 %s", 
                        actigSys, totalDebitAmount, totalCreditAmount);
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
        }

        log.info("凭证平衡性验证通过");
    }

    /**
     * 验证凭证完整性
     * 确保必要字段不为空
     */
    private void validateVoucherCompleteness(List<ErpReimAsstDto> insertAssts) {
        log.info("开始验证凭证完整性");

        for (ErpReimAsstDto asst : insertAssts) {
            // 验证必要字段
            if (asst.getActigSys() == null || asst.getActigSys().trim().isEmpty()) {
                throw new RuntimeException("辅助项会计体系不能为空");
            }
            
            if (asst.getActigAmtType() == null || asst.getActigAmtType().trim().isEmpty()) {
                throw new RuntimeException("辅助项借贷方向不能为空");
            }
            
            if (asst.getActigSubCode() == null || asst.getActigSubCode().trim().isEmpty()) {
                throw new RuntimeException("辅助项会计科目代码不能为空");
            }
            
            if (asst.getActigSubName() == null || asst.getActigSubName().trim().isEmpty()) {
                throw new RuntimeException("辅助项会计科目名称不能为空");
            }
            
            if (asst.getActigAmt() == null) {
                throw new RuntimeException("辅助项金额不能为空");
            }
            
            if (asst.getSupType() == null || asst.getSupType().trim().isEmpty()) {
                throw new RuntimeException("辅助项业务类型不能为空");
            }
            
            if (asst.getAsstNo() == null || asst.getAsstNo() <= 0) {
                throw new RuntimeException("辅助项序号必须大于0");
            }
        }

        log.info("凭证完整性验证通过");
    }

    /**
     * 验证凭证金额
     * 确保金额为正数
     */
    private void validateVoucherAmounts(List<ErpReimAsstDto> insertAssts) {
        log.info("开始验证凭证金额");

        for (ErpReimAsstDto asst : insertAssts) {
            if (asst.getActigAmt().compareTo(BigDecimal.ZERO) <= 0) {
                String errorMsg = String.format("辅助项金额必须大于0，当前金额: %s，序号: %s", 
                        asst.getActigAmt(), asst.getAsstNo());
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
        }

        log.info("凭证金额验证通过");
    }

    /**
     * 验证凭证科目
     * 确保科目代码符合规范
     */
    private void validateVoucherSubjects(List<ErpReimAsstDto> insertAssts) {
        log.info("开始验证凭证科目");

        for (ErpReimAsstDto asst : insertAssts) {
            String actigSubCode = asst.getActigSubCode();
            
            // 验证科目代码格式（应该是数字格式）
            if (!actigSubCode.matches("\\d+")) {
                String errorMsg = String.format("会计科目代码格式不正确: %s，应为数字格式", actigSubCode);
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
            
            // 验证科目代码长度（通常为9位）
            if (actigSubCode.length() != 9) {
                log.warn("会计科目代码长度可能不正确: {}，标准长度为9位", actigSubCode);
            }
            
            // 验证会计体系与科目代码的匹配性
            if (MedConst.TYPE_1.equals(asst.getActigSys())) { // 财务会计
                if (!isFinancialSubjectCode(actigSubCode)) {
                    log.warn("财务会计科目代码可能不正确: {}", actigSubCode);
                }
            } else if (MedConst.TYPE_2.equals(asst.getActigSys())) { // 预算会计
                if (!isBudgetSubjectCode(actigSubCode)) {
                    log.warn("预算会计科目代码可能不正确: {}", actigSubCode);
                }
            }
        }

        log.info("凭证科目验证通过");
    }

    /**
     * 判断是否为财务会计科目代码
     */
    private boolean isFinancialSubjectCode(String code) {
        // 财务会计科目通常以1、2、3、4、5、6开头
        return code.startsWith("1") || code.startsWith("2") || code.startsWith("3") 
            || code.startsWith("4") || code.startsWith("5") || code.startsWith("6");
    }

    /**
     * 判断是否为预算会计科目代码
     */
    private boolean isBudgetSubjectCode(String code) {
        // 预算会计科目通常以5、6开头
        return code.startsWith("5") || code.startsWith("6");
    }
} 