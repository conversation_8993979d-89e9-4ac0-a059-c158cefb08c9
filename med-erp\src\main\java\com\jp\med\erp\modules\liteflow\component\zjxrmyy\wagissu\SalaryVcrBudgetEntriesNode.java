package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.constant.MedConst;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 预算科目凭证生成节点
 * 处理各类预算科目相关的凭证生成，按原业务逻辑实现完整的预算科目计算
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrBudgetEntriesNode", name = "预算科目凭证生成")
public class SalaryVcrBudgetEntriesNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Override
    public void process() throws Exception {
        log.info("开始执行预算科目凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        // 获取个人代扣任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
                .stream()
                .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.INDIVDUAL_REDUCE))
                .collect(Collectors.toList());

        if (taskDetails.isEmpty()) {
            log.info("没有个人代扣任务明细，跳过预算科目处理");
            return;
        }

        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理预算科目-借方和贷方
            insertAssts.addAll(processBudgetEntries(baseParamDtos, flowContext, taskDetails));

        } catch (Exception e) {
            log.error("预算科目凭证生成失败", e);
            flowContext.markFlowFailed("预算科目凭证生成失败: " + e.getMessage());
            throw e;
        }

        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);

        log.info("预算科目凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理预算科目
     */
    private List<ErpReimAsstDto> processBudgetEntries(SalaryVcrBaseParamVo baseParamDtos,
                                                      SalaryVcrFlowContextVo flowContext,
                                                      List<SalaryVcrTaskDetialVo> taskDetails) {
        log.info("开始处理预算科目");

        List<ErpReimAsstDto> result = new ArrayList<>();
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
                .getOrDefault(ErpConstants.BUSIVOUCHERTYPE_SALARY_BUDGET, new ArrayList<>());

        if (templates.isEmpty()) {
            log.warn("未找到预算科目凭证模版配置");
            return result;
        }

        // 查询工资计提辅助项，获取预算科目计算基础数据
        Integer accrueTaskId = baseParamDtos.getSalaryTaskMap().get(ErpConstants.SALARY);
        ErpReimAsstDto asstParam = new ErpReimAsstDto();
        asstParam.setSupType(MedConst.TYPE_3);
        asstParam.setReimDetailId(accrueTaskId);
        List<ErpReimAsstVo> jtAssts = erpVcrDetailReadMapper.queryReimAsstVoList(asstParam);

        if (jtAssts.isEmpty()) {
            log.warn("未找到工资计提辅助项数据，无法进行预算科目计算");
            return result;
        }

        // 获取银行存款金额（从上下文中获取）
        BigDecimal bankDepositAmount = getBankDepositAmountFromContext(flowContext);

        // 处理预算科目-借方（按原逻辑计算各项预算科目金额）
        result.addAll(processBudgetDebitEntries(templates, flowContext, jtAssts, bankDepositAmount));

        // 处理预算科目-贷方（统一贷方处理）
        result.addAll(processBudgetCreditEntries(templates, flowContext, bankDepositAmount));

        log.info("预算科目处理完成，生成 {} 个辅助项", result.size());
        return result;
    }

    /**
     * 处理预算科目借方
     */
    private List<ErpReimAsstDto> processBudgetDebitEntries(List<VoucherTemplateDto> templates,
                                                           SalaryVcrFlowContextVo flowContext,
                                                           List<ErpReimAsstVo> jtAssts,
                                                           BigDecimal bankDepositAmount) {
        List<ErpReimAsstDto> result = new ArrayList<>();

        // 1.薪级工资
        BigDecimal sgaTotal = jtAssts.stream()
                .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.SAL_GRADE_SALARY_CODE))
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (sgaTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.SAL_GRADE_SALARY,
                    sgaTotal, "个人代扣-预算科目-薪级工资", ErpConstants.SAL_GRADE_SALARY_CODE));
        }

        // 2.护士10%
        BigDecimal nsTotal = jtAssts.stream()
                .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.NURSE_SALARY_CODE))
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (nsTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.NURSE_SALARY,
                    nsTotal, "个人代扣-预算科目-护士10%",ErpConstants.NURSE_SALARY_CODE));
        }

        // 3.地区附加津贴
        BigDecimal asTotal = jtAssts.stream()
                .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.AREA_SALARY_CODE))
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (asTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.AREA_SALARY,
                    asTotal, "个人代扣-预算科目-地区附加津贴", ErpConstants.AREA_SALARY_CODE));
        }

        // 4.护龄补贴
        BigDecimal agTotal = jtAssts.stream()
                .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.AGE_SALARY_CODE))
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (agTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.AGE_SALARY,
                    agTotal, "个人代扣-预算科目-护龄补贴", ErpConstants.AGE_SALARY_CODE));
        }

        // 5.基础绩效工资
        BigDecimal bpTotal = jtAssts.stream()
                .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.BASIC_PERF_CODE))
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (bpTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.BASIC_PERF,
                    bpTotal, "个人代扣-预算科目-基础绩效工资",ErpConstants.BASIC_PERF_CODE));
        }

        // 6.生活补贴
        BigDecimal lsTotal = jtAssts.stream()
                .filter(e -> StringUtils.equals(e.getEconSubCode(), ErpConstants.LIFE_SALARY_CODE))
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (lsTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.LIFE_SALARY,
                    lsTotal, "个人代扣-预算科目-生活补贴",ErpConstants.LIFE_SALARY_CODE));
        }

        // 7.岗位工资（按原逻辑计算：银行存款-薪级工资-护士10%-地区附加津贴-护龄津贴-基础性绩效-生活补贴）
        BigDecimal psTotal = bankDepositAmount.subtract(sgaTotal).subtract(nsTotal)
                .subtract(asTotal).subtract(agTotal).subtract(bpTotal).subtract(lsTotal);

        if (psTotal.compareTo(BigDecimal.ZERO) > 0) {
            result.addAll(createBudgetDebitEntry(templates, flowContext, ErpConstants.POST_SALARY,
                    psTotal, "个人代扣-预算科目-岗位工资",ErpConstants.POST_SALARY_CODE));
        }

        return result;
    }

    /**
     * 处理预算科目贷方
     */
    private List<ErpReimAsstDto> processBudgetCreditEntries(List<VoucherTemplateDto> templates,
                                                            SalaryVcrFlowContextVo flowContext,
                                                            BigDecimal bankDepositAmount) {
        List<ErpReimAsstDto> result = new ArrayList<>();

        // 预算科目贷方统一处理（金额为银行存款金额）
        SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail("budgetEntries",
                bankDepositAmount, "个人代扣-预算科目-贷方",null);

        Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
        if (matchedTemplate.isPresent()) {
            List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                    matchedTemplate.get(), virtualDetail, flowContext);
            result.addAll(detailAssts);
            log.debug("预算科目贷方应用模版生成 {} 个辅助项", detailAssts.size());
        }

        return result;
    }

    /**
     * 创建预算科目借方分录
     */
    private List<ErpReimAsstDto> createBudgetDebitEntry(List<VoucherTemplateDto> templates,
                                                        SalaryVcrFlowContextVo flowContext,
                                                        String budgetItem,
                                                        BigDecimal amount,
                                                        String description, String econSubCode) {
        List<ErpReimAsstDto> result = new ArrayList<>();

        SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(budgetItem, amount, description, econSubCode);

        Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
        if (matchedTemplate.isPresent()) {
            List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                    matchedTemplate.get(), virtualDetail, flowContext);
            result.addAll(detailAssts);
            log.debug("预算科目 {} 应用模版生成 {} 个辅助项", budgetItem, detailAssts.size());
        }

        return result;
    }

    /**
     * 从上下文获取银行存款金额
     */
    private BigDecimal getBankDepositAmountFromContext(SalaryVcrFlowContextVo flowContext) {
        List<ErpReimAsstDto> generatedAssts = flowContext.getGeneratedAssts();
        if (generatedAssts == null || generatedAssts.isEmpty()) {
            log.warn("上下文中未找到已生成的辅助项，无法获取银行存款金额");
            return BigDecimal.ZERO;
        }

        // 查找银行存款相关的辅助项
        return generatedAssts.stream()
                .filter(asst -> asst.getAbst() != null && asst.getAbst().contains("银行存款"))
                .filter(asst -> ErpConstants.ACTIGSYS_TYPE_1.equals(asst.getActigSys())) // 财务会计
                .filter(asst -> ErpConstants.AMOUNT_TYPE_2.equals(asst.getActigAmtType())) // 贷方
                .map(ErpReimAsstDto::getActigAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description, String econSubCode) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        detail.setEconSubCode(econSubCode);
        return detail;
    }
} 