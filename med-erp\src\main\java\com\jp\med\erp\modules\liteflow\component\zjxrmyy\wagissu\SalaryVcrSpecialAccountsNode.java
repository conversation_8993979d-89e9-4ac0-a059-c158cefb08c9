package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.feign.hrm.HrmEmployeeSalaryFeignService;
import com.jp.med.common.vo.EmployeeReallySalaryVo;
import com.jp.med.common.vo.HrpSalaryTask;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特殊往来账凭证生成节点
 * 处理维修班、总务科、住院收费室、单采血浆站等特殊人员的往来账凭证
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrSpecialAccountsNode", name = "特殊往来账凭证生成")
public class SalaryVcrSpecialAccountsNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Autowired
    private HrmEmployeeSalaryFeignService hrmEmployeeSalaryFeignService;

    @Override
    public void process() throws Exception {
        log.info("开始执行特殊往来账凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        // 获取个人代扣任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
                .stream()
                .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.INDIVDUAL_REDUCE))
                .collect(Collectors.toList());

        if (taskDetails.isEmpty()) {
            log.info("没有个人代扣任务明细，跳过特殊往来账处理");
            return;
        }

        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理特殊往来账
            insertAssts.addAll(processSpecialAccounts(baseParamDtos, flowContext));

        } catch (Exception e) {
            log.error("特殊往来账凭证生成失败", e);
            flowContext.markFlowFailed("特殊往来账凭证生成失败: " + e.getMessage());
            throw e;
        }

        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);

        log.info("特殊往来账凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理特殊往来账
     */
    private List<ErpReimAsstDto> processSpecialAccounts(SalaryVcrBaseParamVo baseParamDtos,
                                                        SalaryVcrFlowContextVo flowContext) {
        log.info("开始处理特殊往来账");

        List<ErpReimAsstDto> result = new ArrayList<>();

        try {
            // 获取在职职工工资明细数据,通过工资任务批次id查询应发明细
            HrpSalaryTask salaryParam = new HrpSalaryTask();
//            salaryParam.setId(baseParamDtos.gets());
            salaryParam.setId(baseParamDtos.getSalaryId());
            CommonResult<List<EmployeeReallySalaryVo>> salaryResult = hrmEmployeeSalaryFeignService.queryReallySalaryDetail(salaryParam);

            if (salaryResult == null || salaryResult.getData() == null) {
                log.warn("无法获取在职职工工资明细数据");
                return result;
            }
            // 设置应发工资明细进入上下文
            flowContext.setEmployeeReallySalaryVoList(salaryResult.getData());

            List<EmployeeReallySalaryVo> salaryData = salaryResult.getData();

            // 处理维修班、总务科、住院收费室、单采血浆站特殊人员
            result.addAll(processMaintenanceStaff(baseParamDtos, flowContext, salaryData));
            result.addAll(processAdministrativeStaff(baseParamDtos, flowContext, salaryData));
            result.addAll(processInpatientChargeStaff(baseParamDtos, flowContext, salaryData));
            result.addAll(processBloodStationStaff(baseParamDtos, flowContext, salaryData));

        } catch (Exception e) {
            log.error("处理特殊往来账失败", e);
        }

        log.info("特殊往来账处理完成，生成 {} 个辅助项", result.size());
        return result;
    }

    /**
     * 处理维修班人员特殊往来账
     */
    private List<ErpReimAsstDto> processMaintenanceStaff(SalaryVcrBaseParamVo baseParamDtos,
                                                         SalaryVcrFlowContextVo flowContext,
                                                         List<EmployeeReallySalaryVo> salaryData) {
        List<String> maintCodes = Arrays.asList("0175", "0580", "0601", "0605", "0633", "0666", "0669", "1261", "1262", "1404", "1406");

        List<EmployeeReallySalaryVo> maintStaff = salaryData.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "528004") && maintCodes.contains(e.getEmpCode()))
                .collect(Collectors.toList());

        return processSpecialStaffByType(baseParamDtos, flowContext, maintStaff,
                ErpConstants.SPECIAL_CORRS_ACCOUNT, "维修班人员-特殊往来账");
    }

    /**
     * 处理总务科人员特殊往来账
     */
    private List<ErpReimAsstDto> processAdministrativeStaff(SalaryVcrBaseParamVo baseParamDtos,
                                                            SalaryVcrFlowContextVo flowContext,
                                                            List<EmployeeReallySalaryVo> salaryData) {
        List<EmployeeReallySalaryVo> adminStaff = salaryData.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "528001") && StringUtils.equals(e.getEmpCode(), "1242"))
                .collect(Collectors.toList());

        return processSpecialStaffByType(baseParamDtos, flowContext, adminStaff,
                ErpConstants.SPECIAL_CORRS_ACCOUNT, "总务科人员-特殊往来账");
    }

    /**
     * 处理住院收费室人员特殊往来账
     */
    private List<ErpReimAsstDto> processInpatientChargeStaff(SalaryVcrBaseParamVo baseParamDtos,
                                                             SalaryVcrFlowContextVo flowContext,
                                                             List<EmployeeReallySalaryVo> salaryData) {
        List<EmployeeReallySalaryVo> chargeStaff = salaryData.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "402002") && StringUtils.equals(e.getEmpCode(), "1346"))
                .collect(Collectors.toList());

        return processSpecialStaffByType(baseParamDtos, flowContext, chargeStaff,
                ErpConstants.SPECIAL_CORRS_ACCOUNT, "住院收费室人员-特殊往来账");
    }

    /**
     * 处理单采血浆站人员特殊往来账
     */
    private List<ErpReimAsstDto> processBloodStationStaff(SalaryVcrBaseParamVo baseParamDtos,
                                                          SalaryVcrFlowContextVo flowContext,
                                                          List<EmployeeReallySalaryVo> salaryData) {
        List<EmployeeReallySalaryVo> bloodStaff = salaryData.stream()
                .filter(e -> StringUtils.equals(e.getOrgId(), "405001"))
                .collect(Collectors.toList());

        return processSpecialStaffByType(baseParamDtos, flowContext, bloodStaff,
                ErpConstants.SPECIAL_PLASMA_STATION, "单采血浆站人员-特殊单采血浆站");
    }

    /**
     * 按类型处理特殊人员
     */
    private List<ErpReimAsstDto> processSpecialStaffByType(SalaryVcrBaseParamVo baseParamDtos,
                                                           SalaryVcrFlowContextVo flowContext,
                                                           List<EmployeeReallySalaryVo> staffList,
                                                           String reimName,
                                                           String description) {
        List<ErpReimAsstDto> result = new ArrayList<>();

        if (staffList.isEmpty()) {
            return result;
        }

        List<VoucherTemplateDto> templates = getTemplatesByReimName(baseParamDtos, reimName);

        for (EmployeeReallySalaryVo staff : staffList) {
            if (staff.getShouldPayTotal() != null && staff.getShouldPayTotal().compareTo(BigDecimal.ZERO) > 0) {
                SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(reimName, staff.getShouldPayTotal(), description);
                virtualDetail.setEmpCode(staff.getEmpCode());
                virtualDetail.setOrgId(staff.getOrgId());

                Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
                if (matchedTemplate.isPresent()) {
                    List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                            matchedTemplate.get(), virtualDetail, flowContext);
                    // 设置特殊往来账的数据进入上下文 ,往来账list
                    flowContext.getSpecialAccountsDetailList().add(virtualDetail);
                    result.addAll(detailAssts);
                    log.debug("特殊人员 {} 应用模版生成 {} 个辅助项", staff.getEmpCode(), detailAssts.size());
                }
            }
        }

        return result;
    }

    /**
     * 根据项目名称获取模版
     */
    private List<VoucherTemplateDto> getTemplatesByReimName(SalaryVcrBaseParamVo baseParamDtos, String reimName) {
        // 映射项目名称到业务凭证类型
        String busiVoucherType = mapReimNameToBusiVoucherType(reimName);
        return baseParamDtos.getVoucheTmeplateDtoMap().getOrDefault(busiVoucherType, new ArrayList<>());
    }

    /**
     * 映射项目名称到业务凭证类型
     */
    private String mapReimNameToBusiVoucherType(String reimName) {
        Map<String, String> mapping = new HashMap<>();
        mapping.put(ErpConstants.SPECIAL_CORRS_ACCOUNT, ErpConstants.BUSIVOUCHERTYPE_INTER_COMP_SPEC_ACC);
        mapping.put(ErpConstants.SPECIAL_PLASMA_STATION, ErpConstants.BUSIVOUCHERTYPE_BLO_STATION_SPEC_ACC);

        return mapping.getOrDefault(reimName, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        return detail;
    }
} 