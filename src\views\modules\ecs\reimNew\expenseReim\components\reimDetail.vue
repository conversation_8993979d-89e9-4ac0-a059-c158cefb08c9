<template>
  <!--  @@-processInstanceVariables{{processInstanceVariables}}-->
  <el-card style="height: 100%; width: 100%" :content-style="{ height: '90%' }" ref="cardRef">
    <n-space justify="end" style="margin-top: 10px">
      <n-button
          type="info"
          style="margin-right: 10px"
          @click="showApprInfo"
          v-if="isAudit && (curType == '1' || curType == '2')"
      >{{ curType == '1' ? '差旅费申请审批表' : '培训费申请审批表' }}
      </n-button
      >
      <n-button type="info" @click="printDoc(curType)" v-if="isAudit">打印</n-button>
    </n-space>

    <n-form :model="form" :rules="rules as any" label-placement="left" label-width="80" ref="formRef" :disabled="view">
      <div v-if="showForm">
        <template v-for="(item, idx) in formItems" :key="idx">
          <j-title-line :title="item.title"/>
          <n-row :gutter="20">
            <template v-for="fi in item.formItems" :key="fi.key">
              <n-col :span="fi.span ?? 6" v-if="fi.show == undefined ? true : fi.show">
                <n-form-item :label="fi.title" :path="fi.key">
                  <j-form-item :action-form="form" :item="fi"/>
                </n-form-item>
              </n-col>
            </template>
          </n-row>
        </template>
      </div>

      <!-- 类型为3市，费用报销， -->
      <n-row :gutter="20" v-if="reimType">
        <n-col :span="24">
          <j-title-line :title="reimTable.title"></j-title-line>
          <n-space justify="end" style="margin-bottom: 10px">
            <n-button v-if="!view && curType == '8'" type="info" style="margin-right: 10px" @click="purmChoose"
            >选择采购项目
            </n-button
            >
            <n-button v-if="!view" type="info" style="margin-right: 10px" @click="batchChoose"
            >多个项目选择同一发票
            </n-button
            >
            <n-button v-if="!view && curType == '8'" type="info" style="margin-right: 10px" @click="showInvoInfo"
            >查看发票信息
            </n-button
            >
            <n-button v-if="curType != '9'" type="info" style="margin-right: 10px" @click="addReimItem" 
            >添加报销项
            </n-button
            >
            <!--                  <n-popover v-if="curType != '9'">
                                  <template #trigger>
                                    <j-icon name="add2" style="cursor: pointer" :width="20" :height="20" @click="addReimItem" />
                                  </template>
                                  添加报销项
                                </n-popover>-->
          </n-space>
          <j-n-data-table
              :columns="reimTable.columns"
              :data="reimTable.data"
              :summary="reimTable.summary"
              v-model:checked-row-keys="batchChoosedKeys"
              :max-height="350"
              virtual-scroll
          />
        </n-col>
        <n-col :span="24" v-if="curType != '9'">
          <j-title-line :title="reimTable.budgetTitle"/>
          <j-n-data-table :columns="reimTable.budgetColumns" :data="reimTable.budgetData"></j-n-data-table>
        </n-col>

        <!-- 合同信息展示区域 -->
        <n-col :span="24" v-if="curType == '6'">
          <j-title-line title="当前合同" style="margin-top: 16px">
            <template #default>
              <n-button secondary type="info" @click="toggleContractPreview" style="margin-right: 8px">
                <template #icon>
                  <n-icon><i class="fa fa-file-text-o"></i></n-icon>
                </template>
                合同附件对比
              </n-button>
              <n-button secondary type="primary" @click="showContractDetail">
                <template #icon>
                  <n-icon><i class="fa fa-info-circle"></i></n-icon>
                </template>
                合同详情
              </n-button>
            </template>
          </j-title-line>
          <n-descriptions bordered :column="2" label-placement="left" :label-style="{ width: '200px' }">
            <n-descriptions-item label="合同统一编码">
              {{ details?.formData.ctUnifiedCode }}
            </n-descriptions-item>
            <n-descriptions-item label="合同编码">
              {{ details?.formData.ctCode }}
            </n-descriptions-item>
            <n-descriptions-item label="合同名称" :span="2">
              <strong>{{ details?.formData.ctName }}</strong>
            </n-descriptions-item>
            <n-descriptions-item label="合同相对方" :span="2">
              {{ details?.formData.oppositeName }}
            </n-descriptions-item>
            <n-descriptions-item label="合同总额">
              {{ details?.formData.totalAmt }}
            </n-descriptions-item>
            <n-descriptions-item label="付款阶段">
              {{ details?.formData.stage }}
            </n-descriptions-item>
            <n-descriptions-item label="付款比例"> {{ details?.formData.proportion }}%</n-descriptions-item>
            <n-descriptions-item label="付款方式">
              {{ details?.formData.paymentType === '1' ? '一次性付款' : '分期付款' }}
            </n-descriptions-item>
            <n-descriptions-item label="合同附件">
              <n-button text type="primary" @click="previewContractFile">
                {{ details?.formData.contractAttName }}
              </n-button>
            </n-descriptions-item>
          </n-descriptions>
        </n-col>
      </n-row>

      <!-- 报销人员预算控制 -->
      <AddTraveller
          :view="true"
          :budgetType="budgetType"
          :amt="trainAmt"
          :amt2="narrowTrainAmt"
          :amt3="ZCFAmt"
          :type="curType"
          ref="addTravellerRef"
          v-if="curType == '1' || curType == '2'"
      />

      <!-- 项目信息/补助项目信息 -->
      <div class="exp-space" v-if="curType == '1' || curType == '2'"></div>
      <n-row :gutter="20" v-if="curType == '1' || curType == '2'">
        <n-col :span="12" v-for="tab in tabs" :key="tab.name">
          <j-title-line :title="tab.tab as string"/>
          <j-n-data-table :columns="tab.columns" :data="tab.data" :summary="tab.summary" bordered></j-n-data-table>
        </n-col>
      </n-row>
    </n-form>

    <n-modal v-model:show="showCameraModal" :mask-closable="false">
      <WebCamera @closeModal="showCameraModal = false" @addCameraFile="addCameraFile"/>
    </n-modal>

    <!-- 付款附件 -->
    <div v-if="props.isAudit && isCashAudit && curType != '8' && curType != '10'">
      <j-title-line title="付款证明附件" style="margin-top: 10px"/>
      <n-row gutter="20">
        <n-col :span="12">
          <n-upload :max="100" :file-list="fileList" multiple @update:file-list="fileChange" accept=".png,.jpg,.jpeg">
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                  <archive-icon/>
                </n-icon>
              </div>
              <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传</n-text>
              <n-p depth="3" style="margin: 8px 0 0 0"> 只能上传pdf,png,jpg,jpeg文件格式数据</n-p>
            </n-upload-dragger>
          </n-upload>
        </n-col>
        <n-col :span="12">
          <n-row :gutter="20" style="margin-top: 10px" v-if="['1', '2', '3', '11'].includes(curType)">
            <!-- 差旅、培训、费用报销、费用报销(无发票)才能冲抵借款 -->
            <n-col :span="12">
              <n-form-item label="冲抵借款" label-placement="left">
                <n-select v-model:value="isLoan" :options="yesOrNo" @update:value="changeLoanReimSelection"></n-select>
              </n-form-item>
            </n-col>
            <n-col :span="12" v-show="isLoan == '1'">
              <n-button type="info" @click="changeLoanReimModal">选择借款报销</n-button>
              <span :style="{ color: loanReimId ? 'green' : 'orange', marginLeft: '10px' }">{{
                  loanReimId ? '已选择' : '未选择'
                }}</span>
            </n-col>
          </n-row>
          <n-row :gutter="20">
            <n-col :span="12">
              <n-form-item label="支付类型" label-placement="left">
                <n-select v-model:value="payMethod" :options="payMethodOptions"></n-select>
              </n-form-item>
            </n-col>
          </n-row>
          <n-row :gutter="20">
            <n-button type="info" @click="machineCamera">拍摄上传</n-button>
          </n-row>
        </n-col>
      </n-row>
    </div>

    <!-- 往来单位附件添加 -->
    <div v-if="curType == '4' && !onlyView">
      <j-title-line title="往来单位附件" style="margin-top: 10px"/>
      <n-upload
          :max="1"
          style="width: 50%"
          :file-list="fileList"
          multiple
          @update:file-list="fileChange"
          accept=".xls,.xlsx"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
              <archive-icon/>
            </n-icon>
          </div>
          <n-text style="font-size: 16px">点击或者拖动文件到该区域来上传</n-text>
          <n-p depth="3" style="margin: 8px 0 0 0"> 只能上传xls,xlsx 文件格式数据</n-p>
        </n-upload-dragger>
      </n-upload>
    </div>

    <!-- 其他附件 -->
    <div v-if="['1', '2', '3', '6', '11', '13'].includes(curType)">
      <j-title-line title="其他附件" style="margin-top: 10px"/>
      
      <!-- 已上传附件展示 -->
      <div v-if="ossOtherInfo.length > 0" style="margin-bottom: 16px">
        <div style="display: flex; flex-direction: row; flex-wrap: wrap">
          <a
              href="javascript:void(0)"
              style="color: #18a058; margin-right: 20px"
              v-for="(item, idx) in ossOtherInfo"
              :key="idx"
              @click="itemClick(item)"
          >
            {{ item.name }}
          </a>
        </div>
      </div>
      
      <!-- 附件上传区域 -->
      <div v-if="!onlyView || isPiaojuAudit">
        <n-upload
            :max="100"
            style="width: 50%"
            :file-list="fileList"
            multiple
            @update:file-list="fileChange"
            accept=".pdf,.png,.jpg,.jpeg,.xls,.xlsx,.pptx,.ppt,.doc,.docx"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <archive-icon/>
              </n-icon>
            </div>
            <n-text style="font-size: 16px">点击或者拖动文件到该区域来上传</n-text>
            <n-p depth="3" style="margin: 8px 0 0 0"> 只能上传.pdf,.png,.jpg,.jpeg,.xls,.xlsx,.pptx,.ppt文件格式数据</n-p>
          </n-upload-dragger>
        </n-upload>
      </div>
      
      <!-- 无附件提示 -->
      <n-empty v-if="ossOtherInfo.length == 0 && (onlyView && !isPiaojuAudit)" description="未上传附件"/>
    </div>

    <!-- 分摊附件展示 -->
    <div v-if="onlyView && curType == '4'">
      <j-title-line title="分摊附件" style="margin-top: 10px"/>
      <n-empty description="未上传附件" v-if="ossShareInfo.length == 0"/>
      <div style="display: flex; flex-direction: row; flex-wrap: wrap">
        <a
            href="javascript:void(0)"
            style="color: #18a058; margin-right: 20px"
            v-for="(item, idx) in ossShareInfo"
            :key="idx"
            @click="itemClick(item)"
        >
          {{ item.name }}
        </a>
      </div>
    </div>



    <!-- 付款附件展示 -->
    <div v-if="onlyView && details.formData?.busstas == '1'">
      <j-title-line title="付款证明附件" style="margin-top: 10px"/>
      <n-empty description="未上传附件" v-if="ossPayInfo.length == 0"/>
      <div style="display: flex; flex-direction: row; flex-wrap: wrap">
        <a
            href="javascript:void(0)"
            style="color: #18a058; margin-right: 20px"
            v-for="(item, idx) in ossPayInfo"
            :key="idx"
            @click="itemClick(item)"
        >
          {{ item.name }}
        </a>
      </div>
    </div>

    <!-- <j-preview v-model:show="showPreview" :oss-path="ossInfo.path!" :oss-path-name="ossInfo.name!" :bucket="bucket"/> -->
     <Preview v-model:show="showPreview" :oss-path="ossInfo.path!" :oss-path-name="ossInfo.name!" :bucket="bucket"/>

    <!-- 采购选择项 -->
    <j-modal
        v-model:show="purmsChooseModal"
        width="70%"
        height="70%"
        title="已选发票识别信息"
        :content-style="{ height: 'calc(100% - 85px -56px)' }"
        @confirm="doPurmChoose"
        @close="purmsChooseModal = false"
    >
      <j-crud
          :queryMethod="queryEcsReimPurcTask"
          :queryForm="purmsForm"
          :columns="purmColumns"
          name="零星采购报销"
          ref="crudRef"
          showAddButton
          :paging="false"
          v-model:checked-row-keys="purmsCheckRowKeys"
          :show-operation-button="false"
          :default-expand-all="true"
      >
      </j-crud>
    </j-modal>

    <!-- 收款信息 -->
    <j-title-line title="汇总信息" class="exp-space"/>
    <n-descriptions bordered label-placement="left" :columns="3" :label-style="{ width: '200px' }">
      <n-descriptions-item label="合计金额小写(元)" :content-style="{ position: 'relative' }">
        <span style="position: absolute">{{ sum }}</span>
      </n-descriptions-item>
      <n-descriptions-item label="报销金额合计人民币大写" :span="2" :content-style="{ position: 'relative' }">
        <span style="position: absolute">{{ form.capSum }}</span>
      </n-descriptions-item>
    </n-descriptions>

    <!-- 出差申请 -->
    <ProcessInstanceDetailModal :processInstanceId="currentTravelProcessInstanceId" v-model:show="showTravel"/>

    <!-- 选择发票 -->
    <j-modal
        v-model:show="showInvoChoosePane"
        width="80%"
        height="80%"
        title="个人发票池(请选择对应的发票后再点击确认)"
        :content-style="{ height: 'calc(100% - 85px -56px)' }"
        @close="showInvoChoosePane = false"
        @confirm="doChooseInvo"
    >
      <invo-choose ref="invoChooseRef" @rtInvos="handleRtInvos"/>
    </j-modal>

    <!-- 所选发票识别信息 -->
    <j-modal
        v-model:show="showInvoRecognPane"
        width="70%"
        height="70%"
        title="已选发票识别信息"
        :content-style="{ height: 'calc(100% - 85px -56px)' }"
        @close="showInvoRecognPane = false"
    >
      <j-n-data-table :columns="invoRecognInfo.columns" :data="invoRecognInfo.data"></j-n-data-table>
    </j-modal>

    <!-- 借款报销选择 -->
    <j-modal
        v-model:show="loanReimModal"
        width="80%"
        height="80%"
        title="借款报销选择"
        :content-style="{ height: 'calc(100% - 85px -56px)' }"
        @close="loanReimModal = false"
        @confirm="doLoanReimChoose"
    >
      <j-n-data-table
          :columns="loanInfo.columns"
          :data="loanInfo.data"
          v-model:checked-row-keys="loanReimKeys"
          :row-key="row => row.id"
      />
    </j-modal>
  </el-card>
  <!-- 在最外层添加抽屉组件 -->
  <n-drawer v-model:show="showDrawer" :width="drawerWidth" placement="right">
    <n-drawer-content title="合同详情" closable @close="showDrawer = false">
      <SignedCtReviewDetails :id="details?.formData.contractId" v-if="showDrawer"/>
    </n-drawer-content>
  </n-drawer>
</template>
<script lang="ts" setup>
import {computed, h, nextTick, onMounted, PropType, ref, toRefs, VNode, watch} from 'vue'
import {queryEcsInvoRcdRecogn} from '@/api/ecs/reimMgt/invoRcd.ts'
import {
  NButton,
  NInput,
  NInputNumber,
  NPopconfirm,
  NPopover,
  NSelect,
  NTag,
  NTreeSelect,
  TreeSelectOption,
  UploadFileInfo,
} from 'naive-ui'
import {useSysStore, useUserStore} from '@/store'
import {ArchiveOutline as ArchiveIcon} from '@vicons/ionicons5'
import {AuditDetail, ContainerValueType, CRUDColumnInterface, IPageRes, IRes, JTab, Option, SysDict} from '@jtypes'
import JPGlobal from '@jutil'
import {
  addEcsReimDetailNew,
  queryDeptAmt,
  queryEcsReimDetailNew,
  queryEcsReimDoc,
  queryEcsReimDocMultiTrip, // 添加这个import
  queryItemDetail,
  queryLoanReim,
  updateContractItems,
  updateProItems,
  updateReimInfo,
  uploadPayFiles,
} from '@/api/ecs/reimMgt/reimDetail'
import {Addr, HosOrg, Icon as JIcon, UploadPreview} from '@jcomponents'
import {queryEcsEconFunSubCfg} from '@/api/ecs/config/econFunSubCfg'
import {queryDeptBudget} from '@/api/bms/bmsExecute/bmsBudgetResult'
import {queryEcsReimFixedAsst} from '@/api/ecs/config/actigAssetFixCfg'
import {queryHrmOrgAgencyMapNoPage} from '@/api/hrm/hrmOrgMap/OrgAgencyMap'
import Decimal from 'decimal.js'
import {queryEcsCorrsInsCfg} from '@/api/ecs/config/corrsInsCfg'
import {queryEcsTravelAccomStandardNoPage} from '@/api/ecs/config/travelAccomStandardCfg'
import {queryOrg} from '@/api/hrm/hrmOrg'
import {querySelection} from '@/api/hrm/hrmEmp'
import WebCamera from '@/components/common/webCamera/webCamera.vue'
import {propTypes} from '@/utils/bpmAdapter/propTypes.ts'
import {queryDictData} from '@/api/hrm/dictManage/treeSelectDict.ts'
import AddTraveller from '@/views/modules/ecs/reimMgt/expenseReim/components/addTraveller.vue'
import {formatDate} from '@/utils/bpmAdapter/formatTime.ts'
import {queryEcsReimTravelApprNew} from '@/api/ecs/reimMgt/reimTravelAppr.ts'
import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
import InvoChoose from '@/components/common/invoChoose/invoChoose.vue'
import {getSourceFileUrlOutsideChain} from '@/api/common/common.ts'
import {queryRmsProjectMainInfo} from '@/api/rms/projectApply/ProjectMainInfoWeb.ts'
import {queryRmsFundingBudgetCfg} from '@/api/rms/config/FundingBudgetCfgWeb.ts'
import sysDict from '@/views/modules/sys/sysDict/index.vue'
import {queryEcsReimItemToBudgCfg2} from '@/api/ecs/config/reimItemToBudgCfg.ts'
import SignedCtReviewDetails
  from '@/views/modules/cms/contractManager/contractControl/components/signedCtReviewDetails.vue'
import {queryBmsBudgetDeptMapping} from '@/api/bms/bmsConfig/bmsBudgetDeptMapping.ts'
import {queryEcsReimPurcTask} from '@/api/ecs/reimMgt/reimPurcTask.ts'
import {getSalarySummaryExcel, pageQueryEcsReimSalaryTaskNew} from '@/api/ecs/reimMgt/reimSalaryTask.ts'
import Preview from '@/components/common/preview/newindex.vue'

type FormInfoItem = {
  title: string
  formItems: CRUDColumnInterface[]
}

type Costs = {
  key: string
  val: number | null
}

const emits = defineEmits(['close', 'closeInvo', 'openInvo', 'reimLoading'])
const props = defineProps({
  id: propTypes.number.def(undefined),
  runningTasks: {
    type: Array,
  },
  tasks: {
    type: Array,
  },
  processInstanceVariables: {
    type: Object,
  },
  setHandleAuditBefoerFn: {
    type: Function,
  },
  details: {
    type: Object as PropType<any>,
  },
  view: {
    type: Boolean,
    default: true,
  },
  // 类型 1: 差旅费用报销 2：科研费用报销 3:费用报销
  type: {
    type: String,
    required: true,
  },
  //
  /*addressData: {
    type: Array,
  },*/
  // 是否为申请点击的报销
  apprFlag: {
    type: Boolean,
    default: false,
  },
  // 是否显示审核步骤
  showAuditSteps: {
    type: Boolean,
    default: false,
  },
  // 是否审核
  isAudit: {
    type: Boolean,
    default: true,
  },
  //报销审核时，最后的出纳是否不展示
  reimLastNoShow: {
    type: Boolean,
    default: false,
  },
  //是否重新编辑
  resubmit: {
    type: Boolean,
    default: false,
  },
})

const userStore = useUserStore()
const sysStore = useSysStore()
const formRef = ref()
const cardRef = ref()
const addTravellerRef = ref()
const auditStepsRef = ref()
const invoChooseRef = ref()
let showTravel = ref(false) //差旅、培训申请审批modal是否展示
const currentTravelProcessInstanceId = ref()
let pageLoaded = ref(true)
let details = ref({
  formData: {},
  itemDetails: [],
  subsItemDetails: [],
  psnDetails: [],
  reimAsstDetails: [],
  fileRecords: [],
}) //表单信息
let curType = ref()
let budgetType = ref('')
let onlyView = computed(() => {
  // 在piaoju节点时，只有类型为1（差旅费报销）或2（培训报销）才允许编辑申请信息
  if (isPiaojuAudit.value && (curType.value == '1' || curType.value == '2')) {
    return false
  }
  return props.view
}) //是否只允许看
let show = ref(false)
let reimFixAsstDetails = ref([]) //固定辅助项列表
let econFunOptions = ref<Option[]>([]) // 经济科目
let orgAgencyMap = ref([]) // 不同机构科室对应关系
let costs = ref<Costs[]>([])
let auditDetails = ref<AuditDetail[]>([])
let fileIdentifierMap = <any>{}
let relCoCodeOptions = ref<Option[]>([]) //往来单位
let fileList = ref<Array<UploadFileInfo>>([])
let travelAccomStandardList = ref([]) //差旅住宿费标准
let ossShareInfo = ref<Array<any>>([]) //分摊附件文件
let ossPayInfo = ref<Array<any>>([]) //付款证明文件
let ossOtherInfo = ref<Array<any>>([]) //其他附件文件
let ossInfo = ref<{ path?: string; name?: string }>({}) //付款证明文件项
let showPreview = ref(false) //附件预览
let isApprAccountAudit = ref(false) //是否是申请的会计审核节点
let isOrdinaryAudit = ref(false) //是否是可选类别的审核
let canSubmit = ref(true) //是否能提交表单
let absInBlock = ref(true) //审核时，填写备注是否在方块
let deptInfos = ref<any[]>([]) //科室类型数据
let deptInfoOptions = ref<TreeSelectOption[]>([]) //科室类型数据Options
let bucket = ref('ecs') //预览默认桶
let showCameraModal = ref(false) //高拍仪拍照
let bpmParams = <any>{} //bpm所需参数
let addressData = ref([]) //地区信息
let showForm = ref(false) //展示表单
let isInvoAudit = ref() //是否票据审核
let isCashAudit = ref() //是否出纳审核
let isCWLeader = ref() //是否是财务负责人
let isPiaojuAudit = ref(false) //是否票据(piaoju)审核节点
let showInvoChoosePane = ref(false) //是否显示发票池界面
let showInvoRecognPane = ref(false) //显示发票识别信息
let projectOptoins = ref([]) //科研项目
let fundingOptions = ref([]) //经费类型
let isLoan = ref('0') //是否冲抵借款  冲抵借款报账不需要上传付款证明文件
let loanReimId = ref() //冲抵借款报销id
let loanAmt = ref(0) //冲抵借款报销金额
let loanInfo = ref({
  //借款报销信息
  columns: [
    {
      title: '#',
      key: 'selection',
      multiple: false,
      type: ContainerValueType.SELECTION,
    },
    {
      title: '业务流水号',
      key: 'id',
      width: 100,
    },
    {
      title: '申请人',
      key: 'appyer',
      width: 100,
      render: (row: any) => row.appyerName,
    },
    {
      title: '报销科室',
      key: 'appyerDept',
      width: 100,
      render: (row: any) => row.appyerDeptName,
    },
    {
      title: '申请时间',
      key: 'appyerTime',
      width: 150,
    },
    {
      title: '报销金额',
      key: 'sum',
      width: 140,
    },
    {
      title: '未冲抵金额',
      key: 'noLoanAmt',
      width: 140,
    },
  ],
  data: [],
  checkedRowKeys: [],
})
let loanReimKeys = ref([]) //冲抵借款报销选择的项
let payMethod = ref('0') //是否现金支付  现金支付不上传付款证明文件
let batchChoosedKeys = ref([]) //准备选择同一发票勾选的项
let invoBatchChoosed = ref(false) //是否多个项目选择同一发票
let paymentInfoOptions = ref<any[]>([]) //收款信息options
let purmsChooseModal = ref(false) //采购项目项选择弹窗
const travelApprDetailRef = ref()
let purmsCheckRowKeys = ref([]) //采购项目选择的项
let purmsForm = ref({
  type: '8',
  reimFlag: '0',
})
let loanReimModal = ref(false) //借款报销选择弹窗显示
let form = ref<any>({
  empCode: '', // 员工编号
  appyer: '', // 申请人
  bank: '中国银行', // 开户银行
  acctname: '', // 户名
  bankcode: '', // 银行账(卡)号
  oppositeName: '', //收款乙方名称
  sum: 0, // 合计金额小写(元)
  shareAmt: null,
  capSum: '', // 合计金额大写
  busMet: '', // 出差性质
  appyerDept: '', // 报销/申请科室
  evectionAddrName: '', // 出差地点,
  att: null,
  attName: '',
  shareInvo: null, //分摊发票
  projectId: null, //科研项目id
  fundingId: null, //经费类别id
})

let reimType = computed(() => {
  return ['3', '6', '7', '8', '9', '10', '11', '13'].includes(curType.value)
})
let shareType = computed(() => {
  return curType.value == '4'
})

let selectedRow = ref()

// 文件改变
const fileChange = (files: Array<UploadFileInfo>) => {
  fileList.value = files
}

const yesOrNo = JPGlobal.getDictByType('YES_OR_NO') as Option[]

const payMethodOptions = ref([
  {
    label: '非现金支付',
    value: '0',
  },
  {
    label: '现金支付',
    value: '1',
  },
  {
    label: '复明工程',
    value: '2',
  },
])

const purmColumns = ref([
  {
    title: '#',
    key: 'selection',
    type: ContainerValueType.SELECTION,
    show: false,
    disabled: (row: any) => row.reimFlag == '1',
  },
  {
    title: '项目名称',
    key: 'itemName',
    width: 200,
    render: (row: any) => {
      return h('span', {style: {fontWeight: row.children ? 'bold' : 'normal'}}, row.itemName)
    },
  },
  {
    title: '项目编码',
    key: 'itemNo',
    width: 150,
  },
  {
    title: '报销科室',
    key: 'reimOrgName',
    width: 100,
  },
  {
    title: '金额(元)',
    key: 'sumamt',
    width: 100,
  },
  {
    title: '报销标志',
    key: 'reimFlag',
    width: 100,
    render: (row: any) => {
      return h(
          NTag,
          {size: 'small', round: true, type: row.reimFlag == '1' ? 'success' : 'error'},
          row.reimFlag == '1' ? '已报销' : '待报销'
      )
    },
  },
  {
    title: '执行人',
    key: 'exeEmpName',
    width: 100,
  },
  {
    title: '执行日期',
    key: 'exeDate',
    width: 100,
  },
  {
    title: '执行备注',
    key: 'reimDesc',
    width: 100,
  },
  {
    title: '签收科室',
    key: 'cfmOrgName',
    width: 100,
  },
  {
    title: '签收人',
    key: 'cfmEmpName',
    width: 100,
  },
  {
    title: '签收日期',
    key: 'cfmDate',
    width: 100,
  },
])

const reimTable = ref({
  title: '报销详情',
  columns: [
    {
      type: 'selection',
    },
    {
      title: '序号',
      key: 'index',
      align: 'center',
      width: 90,
    },
    {
      title: '报销摘要',
      key: 'reimAbst',
      render: (row: any) => {
        return h(NInput, {
          value: row.reimAbst,
          type: 'textarea',
          autosize: true,
          disabled: onlyView.value,
          onInput: (val: string) => {
            row.reimAbst = val
          },
        })
      },
    },
    {
      title: '报销科室',
      key: 'deptCode',
      width: 260,
      render: (row: any) => {
        return h(HosOrg, {
          value: row.deptCode,
          disabled: onlyView.value,
          clearable: false,
          pinyinSearch: true,
          customRecords: deptInfos.value as any,
          customOptions: deptInfoOptions.value as any,
          onChangeValue: (d: any) => {
            row.deptCode = d.orgId
            row.deptName = d.orgName
            //生成预算表格
            adjustBudget()
          },
        })
      },
    },
    {
      title: '类别',
      key: 'type',
      width: 260,
      render: (row: any) => {
        return h(NTreeSelect, {
          value: row.type,
          filterable: true,
          checkStrategy: 'child',
          disabled: row.disabled ?? onlyView.value,
          keyField: 'value',
          options: econFunOptions.value as any,
          onUpdateValue: (val: string, option: any) => {
            row.type = val
            row.typeName = option.label
            row.budgetCode = option.budgetCode
            row.typeDetail = option
            // 生成预算表格
            adjustBudget()
          },
        })
      },
    },
    {
      title: '金额',
      key: 'amt',
      width: 100,
      render: (row: any, idx: number) => {
        return h(NInputNumber, {
          value: row.amt,
          showButton: false,
          disabled: onlyView.value,
          onUpdateValue: (val: number | null) => {
            let rk = 'jamt' + idx
            let cost = costs.value.find(c => c.key == rk)
            if (cost) {
              cost.val = val
            } else {
              costs.value.push({
                key: rk,
                val,
              })
            }
            changeSummary()
            row.amt = val
          },
        })
      },
    },
    {
      title: '附件',
      key: 'att',
      width: 100,
      align: 'center',
      render: (row: any) => attRender(row),
    },
    {
      title: onlyView.value ? '' : '操作',
      key: 'operation',
      width: onlyView.value ? 0 : 50,
      align: 'center',
      render: (row: any, idx: number): string | VNode => {
        if (onlyView.value) return ''
        return h(
            NPopover,
            {style: 'padding: 5px', showArrow: false},
            {
              trigger: () =>
                  h(
                      NPopconfirm,
                      {
                        positiveButtonProps: {
                          type: 'error',
                          secondary: true,
                        },
                        onPositiveClick: (e: Event) => {
                          e.stopPropagation()
                          let tv = reimTable.value.budgetData.find(v => v.dept == row.deptCode && v.type == row.type)
                          if (tv) {
                            tv.num -= 1
                            tv.reimAmt = tv.reimAmt - (row.amt ?? 0)
                            if (tv.num == 0) {
                              reimTable.value.budgetData = reimTable.value.budgetData.filter(
                                  v => v.dept != row.deptCode && v.type != row.type
                              )
                            }
                          }

                          const {data: rtRest} = toRefs(reimTable.value)
                          rtRest.value.splice(idx, 1)
                          for (let i = 0; i < rtRest.value.length; i++) {
                            rtRest.value[i].index = i + 1
                          }
                          reimTable.value.data = rtRest.value
                          //重新计算costs数据,并重新计算汇总数据
                          let newCosts: any = []
                          reimTable.value.data.forEach((item, num) => {
                            let rk = 'j' + 'amt' + num
                            newCosts.push({
                              key: rk,
                              val: item.amt,
                            })
                          })
                          costs.value = newCosts
                          changeSummary()
                        },
                        onNegativeClick: (e: Event) => {
                          e.stopPropagation()
                        },
                      },
                      {
                        trigger: () =>
                            h(JIcon, {
                              name: 'delete',
                              width: 20,
                              height: 20,
                              style: {cursor: 'pointer', marginRight: '10px'},
                              onClick: (e: Event) => {
                                e.stopPropagation()
                              },
                            }),
                        default: () => '是否删除？',
                      }
                  ),
              default: () => '删除',
            }
        )
      },
    },
  ],
  data: <any[]>[],
  summary: (pageData: any) =>
      getSummary(sumVal1, {
        type: {
          value: '',
        },
        deptCode: {
          value: '',
        },
        reimAbst: {
          value: '',
        },
        operation: {
          value: '',
        },
        index: {
          value: '汇总',
        },
      }),
  budgetTitle: '预算控制',
  budgetColumns: [
    {
      title: '科室',
      key: 'dept',
      width: 200,
      render: (row: any) => {
        return h(HosOrg, {
          value: row.dept,
          disabled: true,
          clearable: false,
          customRecords: deptInfos.value,
          customOptions: deptInfoOptions.value,
          onChangeValue: (d: any) => {
            row.dept = d.orgId
            adjustBudget()
          },
        })
      },
    },
    {
      title: '类别',
      key: 'type',
      width: 200,
      render: (row: any) => {
        return h(NTreeSelect, {
          value: row.type,
          filterable: true,
          disabled: true,
          checkStrategy: 'child',
          keyField: 'value',
          options: econFunOptions.value as any,
          onUpdateValue: (val: string, option: TreeSelectOption) => {
            row.type = val
          },
        }) as any
      },
    },
    {
      title: '报销金额',
      key: 'reimAmt',
      width: 100,
    },
    {
      title: '科室已报销金额',
      key: 'alreadyUsedAmt',
      width: 100,
    },
    {
      title: '科室预算金额',
      key: 'budgetAmt',
      width: 100,
    },
    {
      title: '提醒',
      key: 'state',
      width: 100,
      align: 'center',
      render: (row: any) => {
        if (row.state) {
          switch (row.state) {
            case '1':
              return h(NTag, {type: 'success', size: 'small'}, () => '预算足够')
            case '2':
              return h(NTag, {type: 'error', size: 'small'}, () => '预算不足')
          }
        }
        return ''
      },
    },
  ],
  budgetData: <any[]>[],
})

    const formItemsTemp = computed((): CRUDColumnInterface[] => [
  {
    title: '报销类型',
    key: 'type',
    disabled: true,
    formItemRender: () => {
      const typeMap = {
        '1': '差旅报销',
        '2': '培训报销',
        '3': '其他费用报销',
        '4': '分摊报销',
        '5': '工资报销',
        '6': '合同报销',
        '7': '折旧报销',
        '8': '零星采购',
        '9': '科研报销',
        '10': '物资采购报销',
        '11': '其他费用报销(无发票)',
        '12': '往来支付',
        '13': '借款',
        '14': '临床实验经费报销',
        '15': '版面费报销'
      };
      // 使用映射表转换编码为名称
      const displayValue = typeMap[form.value.type] || form.value.type;
      
      // 根据不同类型选择不同颜色
      let tagType = 'info';
      if (['1', '2','3', '4', '5','6', '7', '8','9', '10', '11'].includes(form.value.type)) {
        tagType = 'success';
      }
      
      return h(NTag, {
        type: tagType,
        size: 'medium',
        round: false,
        bordered: false
      }, {
        default: () => displayValue
      });
    }
  },
  {title: '业务流水号', key: 'id', disabled: true, noValid: true},
  {title: '员工编号', key: 'empCode', disabled: true},
  {title: '申请人', key: 'appyer', disabled: true},
  {
    title: '申请科室',
    key: 'appyerDept',
    show: reimType.value,
    required: true,
    formItemRender: () => {
      return h(
          HosOrg,
          {
            value: form.value.appyerDept,
            disabled: isPiaojuAudit.value ? false : true,
            clearable: false,
            onChangeValue: (d: any) => {
              form.value.appyerDept = d.orgId
            },
          },
          () => {
          }
      )
    },
  },
  {
    title: '出差时间',
    key: 'evectionTime',
    show: curType.value == '1' || curType.value == '2',
    dataType: 'array',
    required: true,
    disabled: isPiaojuAudit.value ? false : true,
    type: ContainerValueType.DATE_RANGE,
  },
  {
    title: '出差地点',
    key: 'evectionAddr',
    width: 100,
    required: true,
    dataType: 'number',
    show: curType.value == '1' || curType.value == '2',
    disabled: isPiaojuAudit.value ? false : true,
    formItemRender: () => {
      return h(Addr, {
        strategy: 'child',
        disabled: isPiaojuAudit.value ? false : true,
        onSelectChange: (option: any) => {
          form.value.evectionAddrName = JPGlobal.findPath(option.code, addressData.value!)
        },
      })
    },
  },
  {
    title: '是否安排伙食',
    key: 'food',
    width: 100,
    show: (curType.value == '1' || curType.value == '2') && !onlyView.value,
    dictType: 'YES_OR_NO',
    type: ContainerValueType.SELECT,
    disabled: isPiaojuAudit.value ? false : true,
    required: true,
  },
  {
    title: '是否安排住宿',
    key: 'stay',
    width: 100,
    dictType: 'YES_OR_NO',
    show: (curType.value == '1' || curType.value == '2') && !onlyView.value,
    type: ContainerValueType.SELECT,
    disabled: isPiaojuAudit.value ? false : true,
    required: true,
  },
  {
    title: '交通方式',
    key: 'trnp',
    width: 100,
    required: true,
    disabled: isPiaojuAudit.value ? false : true,
    show: (curType.value == '1' || curType.value == '2') && !onlyView.value,
    type: ContainerValueType.SELECT,
    dictType: 'REIM_TRNP',
    dataType: 'array',
    multiple: true,
  },
  {
    title: '出差性质',
    key: 'busMet',
    required: true,
    disabled: isPiaojuAudit.value ? false : true,
    type: ContainerValueType.SELECT,
    show: curType.value == '2',
    selectChange: () => {
      deptChange()
    },
    selection: [
      {
        label: '学术会议及短期培训经费',
        value: 'XSHYDQPX',
      },
      {
        label: '进修经费',
        value: 'JXJF',
      },
      {
        label: '医院指令性任务培训经费',
        value: 'YYZLXRWPXJF',
      },
      {
        label: '继续医学教育项目经费',
        value: 'JXYXJYXMJF',
      },
      {
        label: '科研项目培训经费',
        value: 'KYXMPXJF',
      },
    ],
  },
  {
    title: '科研项目',
    key: 'projectId',
    dataType: 'number',
    type: ContainerValueType.SELECT,
    show: true,
    selection: projectOptoins.value,
    placeholder: '请选择',
    selectChange: (val: any) => {
      form.value.fundingId = null
      rules.value['fundingId'].required = false
      if (val) {
        rules.value['fundingId'].required = true
      }
    },
  },
  {
    title: '经费类型',
    key: 'fundingId',
    dataType: 'number',
    type: ContainerValueType.TREE_SELECT,
    show: true,
    options: fundingOptions.value,
    treeProp: {
      key: 'id',
      label: 'label',
    },
  },
  {
    title: '分摊类型',
    key: 'shareType',
    required: true,
    dictType: 'ACTIG_SHARE_TYPE',
    type: ContainerValueType.SELECT,
    show: curType.value == '4',
  },
  {
    title: '分摊总额',
    key: 'shareAmt',
    width: 100,
    required: true,
    show: curType.value == '4',
    dataType: 'number',
    formItemRender: () =>
        h(NInputNumber, {
          value: form.value.shareAmt,
          style: {width: '100%'},
          showButton: false,
          disabled: onlyView.value,
          onUpdateValue: (val: number | null) => {
            //通过给costs赋值，改变sum的值
            let rk = 'jamt0'
            let cost = costs.value.find(c => c.key == rk)
            if (cost) {
              cost.val = val
            } else {
              costs.value.push({
                key: rk,
                val: val,
              })
            }
          },
        }),
  },
  {
    title: '分摊月份',
    key: 'shareDate',
    width: 100,
    required: true,
    show: curType.value == '4',
    type: ContainerValueType.DATE_MONTH,
  },
  {
    title: '资金类型',
    key: 'fundType',
    width: 100,
    disabled: true,
    show: curType.value == '4',
    type: ContainerValueType.SELECT,
    selection: [
      {
        label: '自有资金',
        value: '1',
      },
      {
        label: '财政资金',
        value: '2',
      },
    ],
  },
  {
    title: '发票上传',
    key: 'shareInvo',
    show: curType.value == '4',
    required: false,
    noValid: true,
    formItemRender: () => {
      let render = [
        h(UploadPreview, {
          ossPath: form.value.att,
          ossName: form.value.attName,
          bucket: 'ecs',
          showUpload: false,
          isFile: false,
        }),
      ]
      if (!onlyView.value) {
        render.push(
            h(
                NButton,
                {
                  onClick: () => {
                    showInvoChoosePane.value = true
                  },
                },
                '选择发票'
            )
        )
      }
      return render
    },
  },
  {
    title: getAbstTitle(),
    key: 'evectionRea',
    show:
        curType.value == '1' ||
        curType.value == '2' ||
        curType.value == '4' ||
        curType.value == '8' ||
        curType.value == '10',
    required: true,
    inputType: 'textarea',
    placeholder: '请填写',
    span: 12,
    disabled: isPiaojuAudit.value ? false : (props.view || false),
  },
])

//不同的报销展示的备注标题不同
const getAbstTitle = () => {
  if (curType.value == '4') {
    return '报销摘要'
  }
  if (curType.value == '8' || curType.value == '10') {
    return '备注'
  }
  return '出差事由'
}

const formItemsTemp2 = computed((): CRUDColumnInterface[] => [
  {title: '开户银行', key: 'bank'},
  {title: '户名', key: 'acctname'},
  {
    title: '银行账号',
    key: 'bankcode',
  },
  {title: '收款乙方名称', key: 'oppositeName', show: curType.value == '6'},
])

const formItems = ref<FormInfoItem[]>([
  {
    title: '基本信息',
    formItems: [],
  },
  {
    title: '收款信息',
    formItems: [],
  },
])

const rules: any = ref({})

const buildRules = () => {
  formItems.value.forEach(fi => {
    fi.formItems.forEach(f => {
      if (!f.noValid) {
        rules.value[f.key] = {
          type: f.dataType ? f.dataType : 'string',
          required: f.required,
          trigger: ['blur', 'change'],
          message: '请填写' + f.title,
        }
      }
    })
  })
}

let sumVal1 = ref(0)
let sumVal2 = ref(0)
let tabs = ref<JTab[]>([
  {
    name: '1',
    tab: '项目信息 (在附件列上传对应发票)',
    columns: [
      {
        title: '项目',
        key: 'item',
        width: 90,
        disabled: onlyView.value,
      },
      {
        title: '单据张数',
        key: 'docNum',
        width: 100,
        render: (row: any) => {
          let val = 0
          if (row.att) {
            if (typeof row.att === 'string') {
              val = row.att.split(',').length
            } else {
              val = row.att.length
            }
          }
          return h(NInputNumber, {
            value: val,
            showButton: false,
            disabled: true,
            onUpdateValue: (val: number | null) => {
              row.docNum = val
            },
          })
        },
      },
      {
        title: '天数/往返数/公里',
        key: 'daysOrKilor',
        render: (row: any, idx: number) => {
          return h(NInputNumber, {
            value: row.daysOrKilor,
            showButton: false,
            disabled: row.disabled ?? true,
            onUpdateValue: (val: number | null) => {
              row.daysOrKilor = val
              //获取人员数
              let psnCount = details.value?.psnDetails.length ?? 0
              //差旅、培训报销会计审核可修改天数
              if (isApprAccountAudit.value) {
                if ((row.daysOrKilor == 0 || row.daysOrKilor) && row.std) {
                  row.amt = new Decimal(row.daysOrKilor)
                      .mul(new Decimal(row.std).mul(new Decimal(psnCount)).add(new Decimal(row.extraAmt ?? 0)))
                      .toNumber()
                  //总额改变
                  let rk = 'j' + 'amt' + idx
                  let cost = costs.value.find(c => c.key == rk)
                  if (cost) {
                    cost.val = row.amt
                  } else {
                    costs.value.push({
                      key: rk,
                      val: row.amt,
                    })
                  }
                  changeSummary()
                }
              }
            },
          })
        },
      },
      {
        title: '标准',
        key: 'std',
        // render: (row: any) => getInputRender('std',row),
        render: (row: any) => {
          return h(NInputNumber, {
            value: row.std,
            showButton: false,
            disabled: true,
          })
        },
      },
      {
        title: '额外费用/天',
        key: 'extraAmt',
        // render: (row: any) => getInputRender('extraAmt',row),
        render: (row: any) => {
          return h(NInputNumber, {
            value: row.extraAmt,
            showButton: false,
            disabled: true,
          })
        },
      },
      {
        title: '金额',
        key: 'amt',
        //render: (row: any, idx: number) => getInputRender('amt', row, true, idx, 'j'),
        render: (row: any, idx: number) => {
          return h(NInputNumber, {
            value: row.amt,
            showButton: false,
            // disabled: onlyView.value,
            disabled: row.disabled ?? true,
            onUpdateValue: (val: number | null) => {
              let rk = 'jamt' + idx
              let cost = costs.value.find(c => c.key == rk)
              if (cost) {
                cost.val = val
              } else {
                costs.value.push({
                  key: rk,
                  val,
                })
              }
              changeSummary()
              row.amt = val
            },
          })
        },
      },
      {
        title: '附件',
        key: 'att',
        width: 100,
        align: 'center',
        render: (row: any) => attRender(row),
      },
    ],
    data: [],
    summary: (pageData: any) =>
        getSummary(sumVal1, {
          docNum: {
            value: h(
                'span',
                (pageData as any).reduce((prevValue: number, row: any) => {
                  return (prevValue ? prevValue : 0) + (row.docNum ? row.docNum : 0)
                }, 0)
            ),
          },
        }),
  },
  {
    name: '2',
    tab: '补助项目信息  (在附件列上传对应发票)',
    columns: [
      {title: '补助项目', key: 'item', width: 100},
      {
        title: '天数/餐数/公里',
        key: 'daysOrKilor',
        render: (row: any, idx: number) => {
          return h(NInputNumber, {
            value: row.daysOrKilor,
            showButton: false,
            disabled: row.disabled ?? onlyView.value,
            onUpdateValue: (val: number | null) => {
              row.daysOrKilor = val
              let psnCount = details.value?.psnDetails.length ?? 0
              //差旅、培训报销会计审核可修改天数
              if (isApprAccountAudit.value) {
                if ((row.daysOrKilor == 0 || row.daysOrKilor) && row.std) {
                  row.amt = new Decimal(row.daysOrKilor).mul(new Decimal(row.std)).toNumber()
                  //总额改变
                  let rk = 'b' + 'amt' + idx
                  let cost = costs.value.find(c => c.key == rk)
                  if (cost) {
                    cost.val = row.amt
                  } else {
                    costs.value.push({
                      key: rk,
                      val: row.amt,
                    })
                  }
                  changeSummary()
                }
              }
            },
          })
        },
      },
      {
        title: '标准',
        key: 'std',
        // render: (row: any) => getInputRender('std', row),
        render: (row: any) => {
          return h(NInputNumber, {
            value: row.std,
            showButton: false,
            disabled: true,
          })
        },
      },
      {
        title: '金额',
        key: 'amt',
        render: (row: any, idx: number) => getInputRender('amt', row, true, idx, 'b'),
      },
      {
        title: '附件',
        key: 'att',
        width: 100,
        align: 'center',
        render: (row: any) => attRender(row),
      },
    ],
    data: [],
    summary: (pageData: any) =>
        getSummary(sumVal2, {
          daysOrKilor: {
            value: '',
          },
          std: {
            value: '',
          },
        }),
  },
])

// 求和
let sum = computed(() => {
  let s = 0
  //避免计算精度丢失
  let sDcVal = new Decimal(0)
  for (const cost of costs.value) {
    sDcVal = sDcVal.plus(new Decimal(cost.val ?? 0))
  }
  s = sDcVal.toNumber()
  form.value.capSum = JPGlobal.numberToChineseCapital(s)
  form.value.sum = s
  if (addTravellerRef.value) addTravellerRef.value.apportionAmt()
  adjustBudget()
  return s
})

//获取当前选中行科室
const getDept = () => {
  return [
    {
      dept: selectedRow.value.deptCode,
      deptName: selectedRow.value.deptName,
      reimAmt: selectedRow.value.amt,
    },
  ]
}

// 获取预算科室信息
const getBudgetDept = () => {
  return addTravellerRef.value.budgetData
}
//出纳环节展示借款报销选择界面
const changeLoanReimModal = async () => {
  //查询当前报销申请人的所有借款报销信息
  let loanReimRes = await queryLoanReim({
    appyer: form.value.empCode,
    type: '13',
    busstas: '1',
    hasPz: '1',
  })
  if (loanReimRes.code == 200) {
    loanInfo.value.data = loanReimRes.data
    loanReimModal.value = true
  }
}

//确认借款报销选择
const doLoanReimChoose = () => {
  if (loanReimKeys.value.length == 0) {
    window.$message.warning('请选择借款报销')
    return
  }
  console.log('选择了借款报销')
  let loanReim = loanInfo.value.data.find(item => (item.id = loanReimKeys.value[0]))
  if (loanReim) {
    //冲抵金额是否足够
    loanAmt.value = form.value.sum > loanReim.noLoanAmt ? loanReim.noLoanAmt : form.value.sum
    loanReimId.value = loanReim.id
  }
  loanReimModal.value = false
}

//改变冲抵借款选项
const changeLoanReimSelection = (val, option) => {
  //如果非冲抵借款，重置
  if (val == '0') {
    loanReimId.value = null
    loanAmt.value = 0
    loanReimKeys.value = []
  }
}

//调整预算，type为3时
const adjustBudget = async () => {
  let tempData: any[] = []
  //选择了科室的所有项目
  let depts: any[] = []
  let reimItems: any[] = []
  let valid = false
  reimTable.value.data.forEach(item => {
    if (item.deptCode && item.type) {
      valid = true
      depts.push(item.deptCode)
      reimItems.push(item.type)
    }
  })
  if (valid) {
    // 查询当年报销项目对预算项目数据
    const itemToBudgets = await queryEcsReimItemToBudgCfg2({year: new Date().getFullYear(), type: '2'})
    // 查询当年预算科室映射数据
    const budgetDeptMapping = await queryBmsBudgetDeptMapping({year: new Date().getFullYear(), flag: '1'})
    // 查询预算
    queryDeptBudget({year: new Date().getFullYear(), reimItemCodes: reimItems, orgIds: depts}).then(res => {
      // 查询已报销金额
      queryDeptAmt({type: curType.value}).then(res1 => {
        let alreadyUsed = res1.data
        let budgetAmt = res.data
        let budgetAmtMap: any = {}
        // 将预算金额转成map格式
        //budgetAmt.forEach((b: any) => (budgetAmtMap[b.budgetCode] = b.budgetAmount))

        reimTable.value.data.forEach(v => {
          if (v.typeDetail) {
            let t = tempData.find(t => t.type == v.type && t.dept == v.deptCode)
            // 科室使用金额
            let ti: any = null
            if (alreadyUsed.length > 0) {
              ti = alreadyUsed.find((r: any) => r.dept == v.deptCode && r.type == v.type)
            }
            if (t) {
              t.num = t.num + 1
              t.reimAmt = t.reimAmt + (v.amt ?? 0)
            } else {
              let budgetItem
              //查询部门预算金额  如果是汇总预算，则直接匹配对应预算项的金额，否则需要同时匹配部门和预算项
              const itemToBudget = itemToBudgets.data.find(ib => ib.reimItemCode == v.type)
              if (itemToBudget && itemToBudget.bgtSummary && itemToBudget.bgtSummary == '1') {
                budgetItem = budgetAmt.find((b: any) => b.budgetCode == v.typeDetail.budgetCode)
              } else {
                // 如果当前有部门映射和预算映射
                let smap = budgetDeptMapping.data.find(
                    bdm => bdm.sourceDept == v.deptCode && bdm.sourceBgtCode == v.typeDetail.budgetCode
                )
                // 如果当前只有部门映射
                let smap1 = budgetDeptMapping.data.find(
                    bdm => bdm.sourceDept == v.deptCode && bdm.sourceBgtCode == undefined
                )
                if (smap) {
                  budgetItem = budgetAmt.find(
                      (b: any) => b.orgId == smap.targetDept && b.budgetCode == smap.targetBgtCode
                  )
                } else if (smap1) {
                  budgetItem = budgetAmt.find(
                      (b: any) => b.orgId == smap1.targetDept && b.budgetCode == v.typeDetail.budgetCode
                  )
                } else {
                  budgetItem = budgetAmt.find(
                      (b: any) => b.orgId == v.deptCode && b.budgetCode == v.typeDetail.budgetCode
                  )
                }
                //如果当前部门有映射，则查询映射后的科室
                /*const map = budgetDeptMapping.data.find(bdm => bdm.sourceDept == v.deptCode)
                  if (map) {
                    budgetItem = budgetAmt.find((b: any) => b.orgId == map.targetDept && b.budgetCode == v.typeDetail.budgetCode)
                  } else {
                    budgetItem = budgetAmt.find((b: any) => b.orgId == v.deptCode && b.budgetCode == v.typeDetail.budgetCode)
                  }*/
              }
              tempData.push({
                dept: v.deptCode,
                type: v.type,
                num: 1,
                reimAmt: v.amt ?? 0,
                alreadyUsedAmt: ti && ti.reimAmt ? ti.reimAmt : 0,
                budgetAmt: v.typeDetail.budgetCode ? (budgetItem ? budgetItem.budgetAmount : 0) : 0,
              })
            }
          }
        })

        if (curType.value == '5') {
          //如果是工资凭证，则显示总数
          let salaryBudgetData = []
          let h = {dept: '', type: '', num: 0, reimAmt: 0, alreadyUsedAmt: 0, budgetAmt: 0}
          tempData.forEach(item => {
            h.num += item.num
            h.reimAmt += item.reimAmt
            h.alreadyUsedAmt += item.alreadyUsedAmt
            h.budgetAmt += item.budgetAmt
          })
          salaryBudgetData.push(h)
          tempData = salaryBudgetData
        }

        // 判断预算
        tempData.forEach(t => {
          if (t.reimAmt != undefined && t.alreadyUsedAmt != undefined && t.budgetAmt != undefined) {
            t.state = t.reimAmt + t.alreadyUsedAmt < t.budgetAmt ? '1' : '2'
          }
        })
        reimTable.value.budgetData = tempData
      })
    })
  }
}

// 添加报销项
const addReimItem = () => {
  if (onlyView.value) {
    return
  }
  reimTable.value.data.push({
    key: JPGlobal.guid(),
    index: reimTable.value.data.length + 1,
    reimAbst: '',
    deptCode: '',
    type: '',
    typeName: '',
    budgetCode: null,
    amt: null,
  })
}

const itemClick = (item: any) => {
  bucket.value = 'ecs'
  ossInfo.value.name = item.name
  ossInfo.value.path = item.path
  showPreview.value = true
}

// 科室人员改变（生成科室详情）
const deptChange = () => {
  //因为预算时将培训费下的几种预算都合并为了培训费预算，这里先只进行对PXF的查询，
  budgetType.value = curType.value == '1' ? 'CLF' : form.value.busMet
  // budgetType.value = curType.value == '1' ? 'CLF' : form.value.apprDeptType == '1' ? 'PXF_ZN' : form.value.busMet
}

const attRender = (row: any) => {
  let render = [
    h(UploadPreview, {
      ossPath: row.att,
      ossName: row.attName,
      bucket: 'ecs',
      showUpload: false,
      isFile: false,
    }),
  ]
  if (!onlyView.value && curType.value != '11' && curType.value != '13') {
    render.push(
        h(
            NButton,
            {
              onClick: () => {
                selectedRow.value = row
                showInvoChoosePane.value = true
              },
            },
            '选择发票'
        )
    )
  }
  return render
}

const getSummary = (field: any, props: {}) => {
  return {
    item: {
      value: h('span', {style: {fontWeight: 'bold'}}, '汇总'),
    },
    ...props,
    att: {
      value: '',
    },
    amt: {
      value: field.value,
    },
  }
}

// 表格汇总数据
let changeSummary = () => {
  sumVal1.value = 0
  sumVal2.value = 0
  let dcVal1 = new Decimal(0)
  let dcVal2 = new Decimal(0)
  costs.value.forEach(t => {
    if (t.key.startsWith('j')) {
      dcVal1 = dcVal1.plus(new Decimal(t.val ?? 0))
      //sumVal1.value += t.val ?? 0
    } else {
      dcVal2 = dcVal2.plus(new Decimal(t.val ?? 0))
      //sumVal2.value += t.val ?? 0
    }
  })
  sumVal1.value = dcVal1.toNumber()
  sumVal2.value = dcVal2.toNumber()
}

//培训费、狭义差旅费
let trainAmt = computed(() => {
  //计算培训费金额
  let tAmt = new Decimal(0)
  if (curType.value == '2') {
    tabs.value[0].data.forEach((item: any) => {
      if (item.item == '培训费') {
        tAmt = new Decimal(item?.amt ?? 0)
      }
    })
  } else {
    //计算所有费用(除了租车费)，作为差旅费
    tabs.value[0].data.forEach((item: any) => {
      if (item.item != '租车费') {
        tAmt = tAmt.plus(new Decimal(item?.amt ?? 0))
      }
    })
    tabs.value[1].data.forEach((item: any) => {
      tAmt = tAmt.plus(new Decimal(item?.amt ?? 0))
    })
  }
  return tAmt.toNumber()
})

let narrowTrainAmt = computed(() => {
  //计算狭义培训费金额 （不包含培训费和租车费）
  let ntAmt = new Decimal(0)
  if (curType.value == '2') {
    tabs.value[0].data.forEach((item: any) => {
      if (item.item != '培训费' && item.item != '租车费') {
        ntAmt = ntAmt.plus(new Decimal(item?.amt ?? 0))
      }
    })
    tabs.value[1].data.forEach((item: any) => {
      ntAmt = ntAmt.plus(new Decimal(item?.amt ?? 0))
    })
  }
  return ntAmt.toNumber()
})

let ZCFAmt = computed(() => {
  //计算培训费金额
  let tAmt = new Decimal(0)
  tabs.value[0].data.forEach((item: any) => {
    if (item.item == '租车费') {
      tAmt = new Decimal(item?.amt ?? 0)
    }
  })
  return tAmt.toNumber()
})

// 获取input的render
const getInputRender = (key: string, row: any, isAmount = false, idx: number = -1, prefix: string = '') => {
  return h(NInputNumber, {
    value: row[key],
    showButton: false,
    disabled: row.disabled ?? onlyView.value,
    onUpdateValue: (val: number | null) => {
      if (isAmount) {
        let rk = prefix + key + idx
        let cost = costs.value.find(c => c.key == rk)
        if (cost) {
          cost.val = val
        } else {
          costs.value.push({
            key: rk,
            val,
          })
        }
        changeSummary()
      }
      row[key] = val
    },
  })
}

const invoiceLoadSummary = (row: any, amt: number) => {
  //计算
  // let lessAmt = ((row.amt == undefined || row.amt == 0)?amt:(row.amt>amt?amt:row.amt))
  // row.amt = lessAmt
  let lessAmt
  let busIdx
  if (curType.value == '1' || curType.value == '2') {
    //项目
    if (!row.daysOrKilor || !row.std) {
      //两者存在为空，以发票金额为准
      lessAmt = amt
    } else {
      //计算标准金额与上传金额大小
      let standardAmt: Decimal
      //随行人员
      let psnCount = addTravellerRef.value.busTravellerData.length ?? 0
      if (row.item == '住宿费') {
        //住宿费额外处理
        standardAmt = new Decimal(row.daysOrKilor)
            .mul(new Decimal(row.std))
            .mul(new Decimal(psnCount))
            .add(new Decimal(row.extraAmt ?? 0).mul(row.daysOrKilor))
      } else {
        standardAmt = new Decimal(row.daysOrKilor).mul(new Decimal(row.std))
      }
      lessAmt = standardAmt.toNumber() > amt ? amt : standardAmt.toNumber()
    }
    row.amt = lessAmt
    if ((busIdx = tabs.value[0].data.indexOf(row)) != -1) {
      loadSummary('amt', busIdx, 'j', lessAmt)
    } else if ((busIdx = tabs.value[1].data.indexOf(row)) != -1) {
      //辅助项目
      loadSummary('amt', busIdx, 'b', lessAmt)
    }
  } else if (curType.value == '3') {
    lessAmt = amt
    row.amt = lessAmt
    if ((busIdx = reimTable.value.data.indexOf(row)) != -1) {
      loadSummary('amt', busIdx, 'j', lessAmt)
    }
  } else if (curType.value == '6') {
    lessAmt = amt
    row.amt = lessAmt
    if ((busIdx = reimTable.value.data.indexOf(row)) != -1) {
      loadSummary('amt', busIdx, 'j', lessAmt)
    }
  } else if (curType.value == '11') {
    lessAmt = amt
    row.amt = lessAmt
    if ((busIdx = reimTable.value.data.indexOf(row)) != -1) {
      loadSummary('amt', busIdx, 'j', lessAmt)
    }
  } else if (curType.value == '13') {
    lessAmt = amt
    row.amt = lessAmt
    if ((busIdx = reimTable.value.data.indexOf(row)) != -1) {
      loadSummary('amt', busIdx, 'j', lessAmt)
    }
  }
  changeSummary()
}

// 提交
const submit = async () => {
  emits('closeInvo')

  //判断预算是否满足
  let budgetValid = true
  if (curType.value == '1') {
    //差旅，只比较预算金额和报销金额
    let budgetData = addTravellerRef.value.budgetData
    for (let i = 0; i < budgetData.length; i++) {
      if (budgetData[i].budgetAmt == 0 && Number(budgetData[i].reimAmt) > 0) {
        budgetValid = false
      }
    }
  } else if (curType.value == '2') {
    //培训，同时比较培训、差旅的预算金额和报销金额
    let budgetData = addTravellerRef.value.budgetData
    for (let i = 0; i < budgetData.length; i++) {
      if (
          (budgetData[i].budgetAmt == 0 && Number(budgetData[i].reimAmt) > 0) ||
          (budgetData[i].budgetAmt2 == 0 && Number(budgetData[i].reimAmt2) > 0)
      ) {
        budgetValid = false
      }
    }
  } else {
    //其他  比较报销金额和预算金额
    let budgetData = reimTable.value.budgetData
    for (let i = 0; i < budgetData.length; i++) {
      if (budgetData[i].budgetAmt == 0 && Number(budgetData[i].reimAmt) > 0) {
        budgetValid = false
      }
    }
  }

  if (!budgetValid) {
    window.$message.error('存在没有预算金额的项目，请检查')
    return
  }

  if (!(await confirmAuditProcess())) {
    return
  }

  let formData = new FormData()
  formRef.value.validate((errors: any) => {
    if (!errors) {
      form.value.appyer = form.value.empCode
      formData.append('type', curType.value!)
      // 添加表单数据
      for (let key in form.value) {
        if (!formData.has(key) && form.value[key]) {
          formData.append(key, (form.value as any)[key])
        }
      }

      // 添加项目
      if (['1', '2'].includes(curType.value)) {
        //如果安排了住宿和伙食，则不能填写住宿费和伙食费
        if (props.details?.formData.food == '1') {
          let foodFee = tabs.value[1].data.find((bn: any) => bn.item == '伙食补助费')
          if (foodFee && foodFee.amt) {
            window.$message.warning('已提供伙食，不能填写伙食补助费')
            return
          }
        }

        if (props.details?.formData.stay == '1') {
          let stay = tabs.value[0].data.find((bn: any) => bn.item == '住宿费')
          if (stay && stay.amt) {
            window.$message.warning('已安排住宿，不能填写住宿费')
            return
          }
        }
        let gzFee = tabs.value[1].data.find((bn: any) => bn.item == '公杂费')
        let zjFee = tabs.value[1].data.find(bn => bn.item == '自驾车补助')
        //交通方式为单位派车、租车的子集则不允许报公杂费
        if (form.value.trnp.every(target => ['6', '4'].includes(target))) {
          if (gzFee && gzFee.amt) {
            window.$message.warning('单位派车、租车方式不能报销公杂费')
            return
          }
        }
        //自驾补助不为零的，不能报公杂费
        if (zjFee && zjFee.amt && gzFee && gzFee.amt) {
          window.$message.warning('自驾车补助和公杂费不能同时报销')
          return
        }
        //项目中如果金额填写了，则必须上传发票(除了交通费 )
        let itemWithInvo = true
        tabs.value[0].data.forEach((item: any) => {
          //交通费另外判断
          if (item.item != '交通费' && item.amt && (!item.docNum || item.docNum == 0)) {
            itemWithInvo = false
          } else {
            const isTraNebor = isTrafficeNebor()
            if (!isTraNebor && item.amt && (!item.docNum || item.docNum == 0)) {
              itemWithInvo = false
            }
          }
          if (item.amt && item.item != '交通费' && (!item.docNum || item.docNum == 0)) itemWithInvo = false
        })
        if (!itemWithInvo) {
          window.$message.warning('填写了金额的项目未上传发票')
          return
        }

        //补助项目中，如果报自驾补助费，则必须上传发票 (申请时间为空或者在2024年之后)
        let selfDrive = tabs.value[1].data.find((bn: any) => bn.item == '自驾车补助')
        if (!props.details?.formData?.appyerTime || Number(props.details?.formData?.appyerTime.slice(0, 4)) >= 2025) {
          if (selfDrive && selfDrive.amt && (!selfDrive.invoId || selfDrive.invoId.length == 0)) {
            window.$message.warning('报自驾补助费必须上传发票')
            return
          }
        }

        //判断发票是否重复选择
        const seen = new Set()
        let noRepeat = true
        for (const item of tabs.value[0].data) {
          if (item.invoId) {
            const valueArray = item.invoId.split(',')
            for (const value of valueArray) {
              const trimValue = value.trim()
              if (seen.has(trimValue)) {
                noRepeat = false
              }
              seen.add(trimValue)
            }
          }
        }
        for (const item of tabs.value[1].data) {
          if (item.invoId) {
            const valueArray = item.invoId.split(',')
            for (const value of valueArray) {
              const trimValue = value.trim()
              if (seen.has(trimValue)) {
                noRepeat = false
              }
              seen.add(trimValue)
            }
          }
        }

        if (!noRepeat) {
          window.$message.error('存在重复选择的发票，请检查')
          return
        }

        // 差旅费用和科教
        tabs.value.forEach(tab => {
          let prefix = tab.name == '1' ? 'itemDetails' : 'subsItemDetails'
          for (let i = 0; i < tab.data.length; i++) {
            let td = tab.data[i]
            for (let tdKey in td) {
              let pk = prefix + '[' + i + '].' + tdKey
              if (td[tdKey]) formData.append(pk, td[tdKey])
            }
          }
        })

        let busTravellerData = JSON.parse(JSON.stringify(addTravellerRef.value.busTravellerData))
        let budgetData = addTravellerRef.value.budgetData
        // 添加出差人
        for (let i = 0; i < busTravellerData.length; i++) {
          let td = busTravellerData[i]
          //对金额进行处理
          td.reimAmt = parseFloat(td.reimAmt ?? 0) + parseFloat(td.reimAmt2 ?? 0)
          for (let tdKey in td) {
            formData.append('psnDetails[' + i + '].' + tdKey, (td as any)[tdKey])
          }
        }
        //添加其他附件
        for (let i = 0; i < fileList.value.length; i++) {
          let f = fileList.value[i]
          formData.append('attFiles[' + i + ']', f.file as File)
        }
      } else if (
          curType.value == '3' ||
          curType.value == '6' ||
          curType.value == '8' ||
          curType.value == '10' ||
          curType.value == '11' ||
          curType.value == '13'
      ) {
        if (reimTable.value.data.length == 0) {
          window.$message.error('请添加报销详情信息')
          return
        }
        //报销详情是否填写完整
        if (
            (curType.value != '5' && curType.value != '11' && curType.value != '13') ||
            (curType.value == '5' && form.value.salaryType == '1')
        ) {
          let inValid = false
          let invoValid = false
          reimTable.value.data.forEach(it => {
            if (curType.value == '6') {
              // 合同报销type=6可不选费用类别
              if (!it.deptCode || it.deptCode == '' || !it.amt) {
                inValid = true
              }
            } else {
              // 其它类型依然必选费用类别
              if (!it.deptCode || it.deptCode == '' || !it.type || it.type == '' || !it.amt) {
                inValid = true
              }
            }
            if (!it.invoId || it.invoId == '') invoValid = true
          })
          if (inValid) {
            window.$message.error('报销详情请填写完整')
            return
          }
          if (invoValid) {
            window.$message.error('存在发票未上传的项目')
            return
          }
        }
        //检查发票是否重复选择   (暂时不判断，可多个项目对应同一发票)
        /*let repeat = hasDuplicates(reimTable.value.data, 'invoId')
          if (repeat) {
            window.$message.error('存在重复选择的发票，请检查')
            return
          }*/
        //添加其他附件
        for (let i = 0; i < fileList.value.length; i++) {
          let f = fileList.value[i]
          formData.append('attFiles[' + i + ']', f.file as File)
        }

        // 费用报销
        reimTable.value.data.forEach((item, idx) => {
          for (let itemKey in item) {
            let pk = 'itemDetails[' + idx + '].' + itemKey
            if (item[itemKey]) formData.append(pk, item[itemKey])
          }
        })
      } else if (curType.value == '4') {
        if (!form.value.att) {
          window.$message.warning('发票未上传')
          return
        }

        //添加文件
        for (let i = 0; i < fileList.value.length; i++) {
          let f = fileList.value[i]
          formData.append('attFiles[' + i + ']', f.file as File)
        }
      }

      //添加bpm参数
      for (let key in bpmParams) {
        formData.append(`bpmParams[${key}]`, bpmParams[key])
      }

      for (var [a, b] of formData.entries()) {
        console.log(a, b)
      }

      // return

      emits('reimLoading', true)

      if (canSubmit.value) {
        canSubmit.value = false
        addEcsReimDetailNew(formData)
            .then(res => {
              window.$message.success('新增报销申请成功')
              emits('close')
              emits('reimLoading', false)
            })
            .catch(err => {
              emits('reimLoading', false)
            })
            .finally(() => {
              canSubmit.value = true
            })
      }
    }
  })
}

//确认审核流程
const confirmAuditProcess = async () => {
  try {
    let curDept = sysStore.getDept
    let isFuncDept //是否职能科室
    let flowCode //审核流程code
    let steps //审核流程步骤
    let details //审核流程详情
    const DEP_deptCode = '531001' //采管科部门编码
    let isHigh = form.value.sum > 50000 //当前金额是否大于50000
    let flag = false

    //判断当前科室的类型-职能科室或非职能科室
    //查询所有科室信息
    await queryOrg({}).then((res: any) => {
      if (res.code == 200) {
        let deptInfo = res.data.find((it: any) => it.orgId == curDept)
        if (deptInfo && deptInfo.orgType) {
          isFuncDept = deptInfo.orgType.split(',').includes('1')
        }
      }
    })
    if (isFuncDept == undefined) {
      window.$message.warning('当前科室未维护科室类型，请联系管理员')
      return flag
    }
    bpmParams.isHign = isHigh ? '1' : '0'
    bpmParams.createReason = ''
    if (form.value.evectionRea) {
      bpmParams.createReason = form.value.evectionRea
    } else {
      if (reimTable.value.data.length > 0) {
        bpmParams.createReason = reimTable.value.data[0].reimAbst ?? ''
      }
    }
    bpmParams.isFuncDept = isFuncDept ? '1' : '0'
    if (form.value.apprDeptType && form.value.apprDeptType == '2') {
      //如果是临床的报销，单独走审核流程
      bpmParams.isClinic = '1'
    }
    if (curType.value == '4') {
      //分摊费用单独走审核流程
      bpmParams.isShare = '1'
    }
    /* 采管科不单独走流程
      if (curDept == DEP_deptCode) {
        bpmParams.isPCM = '1'
      }*/

    //判断审核流程第一节点审核人是否确定
    let first = {
      chker: '',
      chkDept: '',
      jobCategory: '514,515,516,517,518',
      dscr: '科室负责人审核',
    }
    if (!first.chker) {
      //不确定，查询当前科室的主任作为第一审核人
      const res = await querySelection({
        pageSize: 20,
        pageNum: 1,
        empCodeOrEmpName: '',
        orgId: curDept,
        hideLoadingBar: true,
        restrictedEmpType: first.jobCategory ?? '',
      })

      if (res.code == 200) {
        let firstAuditor
        if (res.data.records.length == 0) {
          const res1 = await querySelection({
            pageSize: 1000,
            pageNum: 1,
            empCodeOrEmpName: '',
            orgId: '',
            hideLoadingBar: true,
            restrictedEmpType: first.jobCategory ?? '',
          })
          if (res1.code == 200) {
            if (res1.data.records.length == 0) {
              window.$message.error('当前没有满足条件的审核人')
              return false
            } else if (res1.data.records.length > 1) {
              firstAuditor = await confirmAuditor(res1.data.records, first.dscr)
              if (!firstAuditor) {
                window.$message.warning('未选择审核人')
                return false
              }
            } else {
              firstAuditor = res1.data.records[0]
            }
          }
        } else if (res.data.records.length > 1) {
          firstAuditor = await confirmAuditor(res.data.records, first.dscr)
          if (!firstAuditor) {
            window.$message.warning('未选择审核人')
            return false
          }
        } else {
          firstAuditor = res.data.records[0]
        }
        first.chker = firstAuditor.empCode
        first.chkDept = firstAuditor.orgId
        flag = true
      }
    } else {
      flag = true
    }
    bpmParams.firstApprover = first.chker
    //确定，直接为当前审核流程
    return flag
  } catch (error) {
    window.$message.error(error.message)
    return false
  }
}

const confirmAuditor = async (options: any, title: string) => {
  let firstAuditor
  return new Promise((resolve, reject) => {
    window.$dialog.warning({
      title: title ? '请选择' + title : '选择下一审核人',
      content: () =>
          h(NSelect, {
            valueField: 'empCode',
            labelField: 'empName',
            options: options,
            filterable: true,
            clearable: false,
            onUpdateValue(v) {
              firstAuditor = options.find((item: any) => item.empCode == v)
            },
          }),
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: () => {
        if (!firstAuditor) {
          window.$message.warning('请选择下一审核人')
          resolve(null)
        } else {
          resolve(firstAuditor)
        }
      },
      onNegativeClick: (e: Event) => {
        e.stopPropagation()
        resolve(null)
      },
    })
  })
}

const auditConfirmBefore = async (pass: boolean) => {
  if (!pass) {
    return true
  }
  let flag = true
  
  //票据(piaoju)审核节点，保存修改后的申请信息
  if (isPiaojuAudit.value && (curType.value == '1' || curType.value == '2')) {
    flag = false
    try {
      // 构建formData传给后端
      let formData = new FormData()
      for (let key in form.value) {
        if (form.value[key] !== null && form.value[key] !== undefined && key !== 'appyer') {
          // 跳过appyer字段，后面单独设置
          formData.append(key, form.value[key])
        }
      }
      // 确保appyer传的是工号而不是姓名
      formData.append('appyer', form.value.empCode)
      
      // 添加项目信息和补助项目信息
      if (curType.value == '1' || curType.value == '2') {
        tabs.value.forEach(tab => {
          let prefix = tab.name == '1' ? 'itemDetails' : 'subsItemDetails'
          for (let i = 0; i < tab.data.length; i++) {
            let td = tab.data[i]
            for (let tdKey in td) {
              let pk = prefix + '[' + i + '].' + tdKey
              if (td[tdKey]) formData.append(pk, td[tdKey])
            }
          }
        })

        // 添加出差人员信息
        if (addTravellerRef.value) {
          let busTravellerData = JSON.parse(JSON.stringify(addTravellerRef.value.busTravellerData))
          for (let i = 0; i < busTravellerData.length; i++) {
            let td = busTravellerData[i]
            td.reimAmt = parseFloat(td.reimAmt ?? 0) + parseFloat(td.reimAmt2 ?? 0)
            for (let tdKey in td) {
              formData.append('psnDetails[' + i + '].' + tdKey, (td as any)[tdKey])
            }
          }
        }
      } else if (
        curType.value == '3' || curType.value == '11'
      ) {
        // 处理其他费用报销的报销项信息
        reimTable.value.data.forEach((item, idx) => {
          for (let itemKey in item) {
            let pk = 'itemDetails[' + idx + '].' + itemKey
            if (item[itemKey]) formData.append(pk, item[itemKey])
          }
        })
      } else if (curType.value == '4') {
        // 处理分摊费用的信息
        if (!form.value.att) {
          window.$message.warning('发票未上传')
          return false
        }
      }
      
      // 添加已有的其他附件信息（保留原有附件）
      if (ossOtherInfo.value.length > 0) {
        let existingAttPaths = ossOtherInfo.value.map(item => item.path).join(',')
        let existingAttNames = ossOtherInfo.value.map(item => item.name).join(',')
        formData.append('existingAtt', existingAttPaths)
        formData.append('existingAttName', existingAttNames)
      }
      
      // 添加新上传的其他附件文件
      for (let i = 0; i < fileList.value.length; i++) {
        let f = fileList.value[i]
        formData.append('attFiles[' + i + ']', f.file as File)
      }
      
      await updateReimInfo(formData).then((res: any) => {
        if (res.code == 200) {
          window.$message.success('更新申请信息成功')
          flag = true
        }
      })
    } catch (error) {
      window.$message.error('更新申请信息失败')
      return false
    }
  }
  //票据
  if (isInvoAudit.value) {
    if (isApprAccountAudit.value) {
      flag = false
      //更新项目信息和补助项目信息
      let itemForm = new FormData()
      tabs.value.forEach(tab => {
        let prefix = tab.name == '1' ? 'itemDetails' : 'subsItemDetails'
        for (let i = 0; i < tab.data.length; i++) {
          let td = tab.data[i]
          for (let tdKey in td) {
            let pk = prefix + '[' + i + '].' + tdKey
            itemForm.append(pk, td[tdKey])
          }
        }
      })
      // 添加出差人
      let busTravellerData = JSON.parse(JSON.stringify(addTravellerRef.value.busTravellerData))
      // 添加出差人
      for (let i = 0; i < busTravellerData.length; i++) {
        let td = busTravellerData[i]
        //对金额进行处理
        td.reimAmt = parseFloat(td.reimAmt ?? 0) + parseFloat(td.reimAmt2 ?? 0)
        for (let tdKey in td) {
          itemForm.append('psnDetails[' + i + '].' + tdKey, (td as any)[tdKey])
        }
      }
      //更新项目和补助项目信息
      for (let [a, b] of itemForm.entries()) {
        console.log(a, b)
      }

      await updateProItems({
        itemDetails: tabs.value[0].data,
        subsItemDetails: tabs.value[1].data,
        sum: form.value.sum,
        capSum: form.value.capSum,
        psnDetails: busTravellerData,
      }).then((res: any) => {
        if (res.code == 200) {
          window.$message.success('更新项目、补助项目信息成功')
          flag = true
        }
      })
    }
  }

  //票据或者财务负责人审核环节
  if (isInvoAudit.value || isCWLeader.value) {
    if (isOrdinaryAudit.value) {
      flag = false
      //更新类别信息
      let formData = new FormData()
      reimTable.value.data.forEach((item, idx) => {
        for (let key in item) {
          let pk = 'itemDetails[' + idx + '].' + key
          if (item[key]) formData.append(pk, item[key])
        }
      })
      for (let [a, b] of formData.entries()) {
        console.log(a, b)
      }
      //更新items  之前只针对合同，现在针对通用报销类型
      await updateContractItems({
        itemDetails: reimTable.value.data,
      }).then((res: any) => {
        if (res.code == 200) {
          window.$message.success('更新项目信息成功')
          flag = true
        }
      })
    }
  }

  //出纳
  if (isCashAudit.value) {
    flag = false
    //uploadPayFiles
    let formData = new FormData()
    formData.append('id', form.value?.id || '')
    formData.append('attCode', form.value?.attCode || '')
    formData.append('isLoan', isLoan.value)
    if (loanReimId.value) {
    }
    formData.append('payMethod', payMethod.value)
    formData.append('type', curType.value)

    //分摊报销，资金类型
    if (curType.value == '4') {
      if (!form.value?.fundType) {
        window.$message.error('请填写资金类型')
        return false
      }
      formData.append('fundType', form.value.fundType)
    }

    //如果是冲抵借款，则必须选择借款报销
    if (isLoan.value == '1') {
      if (!loanReimId.value) {
        window.$message.warning('请选择借款报销')
        return false
      }
      formData.append('loanReimId', loanReimId.value)
      formData.append('loanAmt', loanAmt.value)
    }

    //冲抵借款(全部冲抵)、现金支付不上传付款证明文件  零星采购和物资采购在后面统一上传付款文件
    if (
        curType.value != '8' &&
        curType.value != '10' &&
        (isLoan.value == '0' || (isLoan.value == '1' && form.value.sum > loanAmt.value)) &&
        payMethod.value == '0'
    ) {
      //需上传付款证明文件
      if (fileList.value.length == 0) {
        window.$message.warning('请上传付款证明文件')
        return false
      }
      for (let j = 0; j < fileList.value.length; j++) {
        formData.append('attFiles[' + j + ']', fileList.value[j].file)
      }

      /*if (curType.value !='5') {
        let pageFile: any = await JPGlobal.pageToPng(cardRef.value.$el)
        formData.append('pageImageFile',pageFile)
      }*/
    }

    for (let [a, b] of formData.entries()) {
      console.log(a, b)
    }

    // return false
    //上传付款证明文件
    await uploadPayFiles(formData).then((res: any) => {
      if (res.code == 200) {
        window.$message.success('修改报销信息成功')
        flag = true
      }
    })
  }
  return flag
}

// 审核确认
const auditConfirm = (flag: boolean) => {
  auditStepsRef.value.auditConfirm(flag)
}

// 关闭
const close = () => {
  emits('close')
}

const deptChangeP = () => {
  deptChange()
  // 类型相同无法触发监听，手动调用
  addTravellerRef.value.deptChange()
}

const getDictListByType = (type: string) => {
  let items: SysDict[] = JPGlobal.getDictByType(type)
  items.sort((a, b) => {
    return (a.sort ?? 0) - (b.sort ?? 0)
  })
  return items
}

//设置收款信息
const setPaymentInfo = (key: any = null, item: any) => {
  form.value.bank = item?.bankName
  form.value.bankcode = item?.bankCode
  form.value.acctname = item?.empName
}

const machineCamera = () => {
  showCameraModal.value = true
}

// 初始化
const init = async () => {
  deptChange()
  if (!onlyView.value && !props.resubmit) {
    //收款信息options
    if (props.details?.formData?.paymentInfo) {
      paymentInfoOptions.value = props.details?.formData?.paymentInfo.map((item: any) => {
        return {
          ...item,
          value: item.id,
          label: item.codeLable,
        }
      })
    }
    form.value.empCode = userStore.getUserInfo.username!
    form.value.appyer = userStore.getUserInfo.nickname!

    // 🆕 合同报销使用合同的使用科室，其他类型使用当前用户科室
    if (curType.value == '6' && props.details?.formData?.useOrg) {
      form.value.appyerDept = props.details.formData.useOrg
      console.log('🎯 合同报销使用合同使用科室:', props.details.formData.useOrg)
    } else {
      form.value.appyerDept = sysStore.getDept
    }
    if (paymentInfoOptions.value.length == 1) {
      setPaymentInfo(paymentInfoOptions.value[0].key, paymentInfoOptions.value[0])
    }
    if (curType.value == '1' || curType.value == '2') {
      let items: SysDict[] = getDictListByType(curType.value == '1' ? 'EVECTION_ITEM' : 'EVECTION_ITEM_JXJY')
      if (items) {
        let td: any[] = []
        items.forEach(i => {
          td.push({
            item: i.label,
          })
        })
        tabs.value[0].data = td
      }
      let subsItem: SysDict[] = getDictListByType(
          curType.value == '1' ? 'EVECTION_SUBS_ITEM' : 'EVECTION_SUBS_ITEM_JXJY'
      )
      if (subsItem) {
        let td: any[] = []
        subsItem.forEach(i => {
          td.push({
            item: i.label,
          })
        })
        tabs.value[1].data = td
      }
      //住宿费标准、额外费用 不允许修改
      // tabs.value[0].data.forEach((item: any) => {
      //   if (item.item == '住宿费') item.accoDisabled = true
      // })
    } else if (curType.value == '5') {
      //工资
      let salaryVal = new Decimal(0)
      let salaryItems = []
      // details.value?.itemDetails.forEach((d: any, idx: number) => {
      //   let itemData = {
      //     index: idx + 1,
      //     reimAbst: d.reimDesc,
      //     deptCode: d.orgId,
      //     type: d.reimType,
      //     amt: d.reimAmt,
      //     budgetCode: d.budgetCode,
      //     empCode: d.empCode,
      //     reimName: d.reimName,
      //     typeDetail: { type: d.reimType, budgetCode: d.budgetCode },
      //   }
      //   salaryVal = salaryVal.plus(new Decimal(d.reimAmt ?? 0))
      //   salaryItems.push(itemData)
      //   costs.value.push({
      //     key: 'jamt' + idx,
      //     val: d.reimAmt ?? 0,
      //   })
      // // })
      // sumVal1.value = salaryVal.toNumber()
      // reimTable.value.data = salaryItems

      let rk = 'jamt0'
      let cost = costs.value.find(c => c.key == rk)
      if (cost) {
        cost.val = new Decimal(props.details.formData.realPay).toNumber()
      } else {
        costs.value.push({
          key: rk,
          val: new Decimal(props.details.formData.realPay).toNumber(),
        })
      }
      getSalarySummaryExcel({id: props.details.formData.id}).then((res: any) => {
        if (res.code === 200) {
          ossOtherInfo.value.push({
            name: res.data.name,
            path: res.data.path,
          })
        }
      })
    } else if (curType.value == '6') {
      //合同
      let salaryVal = new Decimal(0)
      let salaryItems = []
      props.details?.itemDetails.forEach((d: any, idx: number) => {
        let itemData = {
          key: JPGlobal.guid(),
          index: idx + 1,
          reimAbst: d.reimAbst,
          deptCode: d.orgId,
          type: d.reimType,
          amt: d.reimAmt,
          budgetCode: d.budgetCode,
          typeDetail: {type: d.reimType, budgetCode: d.budgetCode},
        }
        salaryVal = salaryVal.plus(new Decimal(d.reimAmt ?? 0))
        salaryItems.push(itemData)
        costs.value.push({
          key: 'jamt' + idx,
          val: d.reimAmt ?? 0,
        })
      })
      sumVal1.value = salaryVal.toNumber()
      reimTable.value.data = salaryItems
    } else if (curType.value == '7') {
      //折旧
    } else if (curType.value == '8' || curType.value == '10') {
      details.value.itemDetails = props.details?.itemDetails
      //零星采购
      let salaryVal = new Decimal(0)
      let salaryItems = []
      details.value?.itemDetails.forEach((d: any, idx: number) => {
        let itemData = {
          key: JPGlobal.guid(),
          index: idx + 1,
          reimAbst: d.reimDesc,
          deptCode: d.orgId,
          type: d.reimType,
          amt: d.reimAmt,
          budgetCode: d.budgetCode,
          typeDetail: {type: d.reimType, budgetCode: d.budgetCode},
        }
        salaryVal = salaryVal.plus(new Decimal(d.reimAmt ?? 0))
        salaryItems.push(itemData)
        costs.value.push({
          key: 'jamt' + idx,
          val: d.reimAmt ?? 0,
        })
      })
      sumVal1.value = salaryVal.toNumber()
      reimTable.value.data = salaryItems
    }
  } else {
    if (curType.value == '1' || curType.value == '2') {
      // 查看获取数据
      const setVal = (field: any, prefix: string, idx: number) => {
        let dcField = new Decimal(0)
        for (let i = 0; i < tabs.value[idx].data.length; i++) {
          let v = tabs.value[idx].data[i].amt
          dcField = dcField.plus(new Decimal(v ?? 0))
          //field.value += v ? v : 0
          costs.value.push({
            key: prefix + 'amt' + i,
            val: v,
          })
        }
        field.value = dcField.toNumber()
      }
      setVal(sumVal1, 'j', 0)
      setVal(sumVal2, 'b', 1)
    } else if (curType.value == '5') {
      getSalarySummaryExcel({id: form.value.travelApprId}).then((res: any) => {
        if (res.code === 200) {
          ossOtherInfo.value.push({
            name: res.data.name,
            path: res.data.path,
          })
        }
      })
      pageQueryEcsReimSalaryTaskNew({id: form.value.travelApprId, pageNum: 1, pageSize: 20}).then((res: any) => {
        let rk = 'jamt0'
        let cost = costs.value.find(c => c.key == rk)
        if (cost) {
          cost.val = new Decimal(res.data.records[0].realPay).toNumber()
        } else {
          costs.value.push({
            key: rk,
            val: new Decimal(res.data.records[0].realPay).toNumber(),
          })
        }
      })
    } else if (
        curType.value == '3' ||
        curType.value == '6' ||
        curType.value == '8' ||
        curType.value == '10' ||
        curType.value == '11' ||
        curType.value == '13'
    ) {
      // 费用报销
      adjustBudget()
    }
  }

  await nextTick(() => {
    if (props.apprFlag) {
      if (curType.value == '1' || curType.value == '2') {
        Object.assign(form.value, props.details?.formData)
        // 处理申请人
        form.value.empCode = props.details?.formData.appyer
        form.value.appyer = props.details?.formData.appyerName
        form.value.trnp = props.details?.formData.trnp.split(',')
        // 处理出差地点name
        form.value.evectionAddrName = JPGlobal.findPath(props.details?.formData.evectionAddr, addressData.value!)
        // 处理差旅申请id
        form.value.travelApprId = form.value.id
        delete form.value.id
        if (form.value.evectionBegnTime && form.value.evectionEndTime) {
          form.value.evectionTime = [form.value.evectionBegnTime, form.value.evectionEndTime]
        }
        //自动填入费用金额标准信息
        autoFillCost()
        // 处理日期
        if (addTravellerRef.value) {
          // 处理出差人员，不允许删除和修改，可以新增
          addTravellerRef.value.setBusTravellerData(props.details?.psnDetails, true)
          deptChangeP()
        }
      } else if (curType.value == '5') {
        //工资
        //处理工资申请id
        form.value.salaryType = props.details.formData.salaryType
        form.value.travelApprId = props.details.formData.id
        form.value.salaryId = props.details.formData.salaryId
        delete form.value.id
      } else if (curType.value == '6') {
        //合同
        //处理合同申请id
        form.value.travelApprId = props.details.formData.id
        form.value.paymentId = props.details.formData.paymentId
        delete form.value.id
        form.value.oppositeName = props.details.formData.oppositeName
      } else if (curType.value == '8') {
        //零星采购
        //处理彩铃采购申请id
        form.value.purcDetailIds = props.details.formData.purcDetailIds
        delete form.value.id
      } else if (curType.value == '10') {
        //物资采购
        form.value.purcDetailIds = props.details.formData.purcDetailIds
        delete form.value.id
      }
    }
  })
}

//发票池选择发票
const doChooseInvo = () => {
  invoChooseRef.value.doChooseInvo()
}

//操作选择的发票
const handleRtInvos = (invos: Array<any>) => {
  showInvoChoosePane.value = false
  let atts = invos.map(invo => invo.att).join(',')
  let attNames = invos.map(invo => invo.attName).join(',')
  let invoIds = invos.map(invo => invo.id).join(',')
  let allValoremTax = invos
      .reduce((sum, item) => {
        if (item.allValoremTax) {
          return sum.add(new Decimal(item.allValoremTax))
        }
        return sum
      }, new Decimal(0))
      .toNumber()
  //判断是否是多个项目选择同一发票
  if (invoBatchChoosed.value) {
    let choosedRows = reimTable.value.data.filter(e => batchChoosedKeys.value.includes(e.key))
    choosedRows.forEach(e => {
      e.att = atts
      e.attName = attNames
      e.invoId = invoIds
      e.docNum = invos.length
      //重新赋值，并重新计算汇总
      // invoiceLoadSummary(e,allValoremTax)
    })
    invoBatchChoosed.value = false
  } else {
    if (curType.value == '4') {
      form.value.att = atts
      form.value.attName = attNames
      form.value.invoId = invoIds
      form.value.shareAmt = allValoremTax
      //通过给costs赋值，改变sum的值
      let rk = 'jamt0'
      let cost = costs.value.find(c => c.key == rk)
      if (cost) {
        cost.val = allValoremTax
      } else {
        costs.value.push({
          key: rk,
          val: allValoremTax,
        })
      }
    } else {
      selectedRow.value.att = atts
      selectedRow.value.attName = attNames
      selectedRow.value.invoId = invoIds
      selectedRow.value.docNum = invos.length
      //重新赋值，并重新计算汇总
      invoiceLoadSummary(selectedRow.value, allValoremTax)
    }
    console.log('@@-selectedRow', selectedRow.value)
  }
}

const autoFillCost = async () => {
  const evectionAddr = props.details?.formData.evectionAddr
  //随行人员数量
  let psnCount = props.details?.psnDetails.length

  //查询差旅费用标准
  await queryEcsTravelAccomStandardNoPage({}).then((res: any) => {
    travelAccomStandardList.value = res.data
    //随行人员信息按性别分组
    const sexGroup = props.details?.psnDetails.reduce((acc, obj) => {
      const key = obj.sex
      if (!acc[key]) {
        acc[key] = []
      }
      acc[key].push(obj)
      return acc
    }, {})
    //是否存在单男单女情况
    let boyCount = sexGroup['318']?.length
    let girlCount = sexGroup['319']?.length
    let boyFee = false
    let girlFee = false
    if (boyCount && boyCount > 0 && boyCount % 2 == 1) {
      boyFee = true
    }
    if (girlCount && girlCount > 0 && girlCount % 2 == 1) {
      girlFee = true
    }

    //获取当前地址差旅费标准
    let standard: any = getStandard(evectionAddr)
    //出差天数
    const startDate = new Date(form.value.evectionBegnTime)
    const endDate = new Date(form.value.evectionEndTime)
    const dayDiff = Math.abs(Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24)) + 1)
    //住宿费计算天数，需减少一天
    const accomDays = dayDiff - 1
    let currPrice = ""
    if (standard) {
      //单人差旅价格
      let travelSum = new Decimal(0)
      if (standard.peakSeason) {
        //如果当前有旺季标准，计算是否是旺季价格
        const seasonArr = standard.peakSeason.split(',')
        let dateStrs = JPGlobal.getDateStrsBetweenDateArea(
            form.value.evectionBegnTime,
            form.value.evectionEndTime,
            'YYYY-MM-DD'
        )
        //获取住宿日期，最后一天不计算
        dateStrs = dateStrs.slice(0, -1)
        dateStrs.forEach(item => {
          //分割出月份，计算是否是旺季月份  2024-5-15
          const month = parseInt(item.slice(5, 7)).toString()
          if (seasonArr.includes(month)) {
            currPrice = standard.peakSeasonPrice
            travelSum = travelSum.add(new Decimal(standard.peakSeasonPrice))
          } else {
            currPrice = standard.standardPrice
            travelSum = travelSum.add(new Decimal(standard.standardPrice))
          }
        })
      } else {
        currPrice = standard.standardPrice
        //直接按标准费用计算出差天数的金额
        travelSum = new Decimal(accomDays).mul(new Decimal(standard.standardPrice))
      }
      //给差旅费填入金额
      const accomCost = tabs.value[0].data.find((item: any) => item.item == '住宿费')
      //未安排住宿才自动填入住宿费
      if (accomCost && props.details?.formData.stay != '1') {
        let amt = travelSum.mul(new Decimal(psnCount))
        if (boyFee || girlFee) {
          //单男单女每天有额外 50
          let sexExtraAmt = new Decimal(0)
          if (boyFee) {
            amt = amt.add(new Decimal(accomDays).mul(new Decimal(50)))
            sexExtraAmt = sexExtraAmt.add(new Decimal(50))
          }
          if (girlFee) {
            amt = amt.add(new Decimal(accomDays).mul(new Decimal(50)))
            sexExtraAmt = sexExtraAmt.add(new Decimal(50))
          }
          accomCost.extraAmt = sexExtraAmt.toNumber()
        }
        accomCost.amt = amt.toNumber()
        accomCost.daysOrKilor = accomDays
        // accomCost.std = standard.peakSeasonPrice ?? standard.standardPrice
        accomCost.std = currPrice
        const idx = tabs.value[0].data.indexOf(accomCost)
        loadSummary('amt', idx, 'j', accomCost.amt)
      }
    }

    const addrLayerArr = getAddrLayer(evectionAddr)

    //计算交通费
    let trafficAddr = [510100, 510600, 510681, 510682, 510683, 510604]
    let trafficFee = {
      510100: 45, //成都市
      510600: 14, //德阳市
      510681: 16, //广汉市
      510682: 23, //什邡市
      510683: 21, //绵竹市
      510604: 17, //德阳市，罗江区
    }

    let trafficeStandard
    if (addrLayerArr[2] && trafficAddr.includes(addrLayerArr[2])) {
      trafficeStandard = trafficFee[addrLayerArr[2]]
    } else if (addrLayerArr[1] && trafficAddr.includes(addrLayerArr[1])) {
      trafficeStandard = trafficFee[addrLayerArr[1]]
    }
    //填入交通费
    const trafficCost = tabs.value[0].data.find((item: any) => item.item == '交通费')
    if (trafficCost && trafficeStandard && !form.value.trnp.includes('5')) {
      trafficCost.std = trafficeStandard
      trafficCost.daysOrKilor = psnCount * 2
      trafficCost.amt = new Decimal(trafficeStandard).mul(new Decimal(psnCount)).mul(new Decimal(2)).toNumber()
      const idx = tabs.value[0].data.indexOf(trafficCost)
      loadSummary('amt', idx, 'j', trafficCost.amt)
    }

    //计算伙食补助费
    let addrLayerCode = [510000, 510600, 510623] //四川省、德阳市、中江县
    let provinceSpec = [513300, 513200, 511900] //省内特殊地区 甘孜、阿坝、凉山
    let citySpec = [510722, 510121] //市内特殊地区 三台、金堂

    let addrItem = addressData.value!.find((item: any) => item.id == evectionAddr)
    let mealSum
    let mealStandard
    if (addrLayerArr[2] && addrLayerArr[2] == addrLayerCode[2]) {
      //县内
      mealStandard = new Decimal(10)
      mealSum = new Decimal(dayDiff).mul(new Decimal(3)).mul(mealStandard)
    } else if (
        (addrLayerArr[2] && citySpec.includes(addrLayerArr[2])) ||
        (addrLayerArr[1] && addrLayerArr[1] == addrLayerCode[1])
    ) {
      //市内、比邻县(三台、金堂)
      mealStandard = new Decimal(20)
      mealSum = new Decimal(dayDiff).mul(new Decimal(3)).mul(mealStandard)
    } else if (
        addrLayerArr[0] &&
        addrLayerArr[0] == addrLayerCode[0] &&
        addrLayerArr[1] &&
        !provinceSpec.includes(addrLayerArr[1])
    ) {
      //省内(除甘孜、阿坝、凉山)
      mealStandard = new Decimal(30)
      mealSum = new Decimal(dayDiff).mul(new Decimal(3)).mul(mealStandard)
    } else {
      //省外(或省内的甘孜、阿坝、凉山)
      mealStandard = new Decimal(40)
      mealSum = new Decimal(dayDiff).mul(new Decimal(3)).mul(mealStandard)
    }
    const mealCost = tabs.value[1].data.find((item: any) => item.item == '伙食补助费')
    //未提供伙食的才自动填写伙食补助费
    if (mealCost && props.details?.formData.food != '1') {
      mealCost.amt = mealSum.mul(new Decimal(psnCount)).toNumber()
      mealCost.daysOrKilor = dayDiff * 3 * psnCount
      mealCost.std = mealStandard.toNumber()
      const idx = tabs.value[1].data.indexOf(mealCost)
      loadSummary('amt', idx, 'b', mealCost.amt)
    }

    //自驾车补助
    //当前交通方式是否包含了自驾
    let selfDriveStandard = new Decimal(1.8)
    const selfDrive = tabs.value[1].data.find((item: any) => item.item == '自驾车补助')
    if (selfDrive && form.value.trnp.includes('5')) {
      selfDrive.std = selfDriveStandard.toNumber()
      selfDrive.daysOrKilor = form.value.kil
      selfDrive.amt = new Decimal(selfDrive.daysOrKilor ?? 0).mul(selfDriveStandard).toNumber()
      const idx = tabs.value[1].data.indexOf(selfDrive)
      loadSummary('amt', idx, 'b', selfDrive.amt)
    }

    //公杂费
    let extraSum = new Decimal(0)
    let extraStandard
    if (addrLayerArr[2] && addrLayerArr[2] == addrLayerCode[2]) {
      //县内,无公杂费
      extraStandard = new Decimal(0)
    } else if (
        (addrLayerArr[1] && addrLayerArr[1] == addrLayerCode[1]) ||
        (addrLayerArr[0] && addrLayerArr[0] == addrLayerCode[0])
    ) {
      //市内或者省内
      extraStandard = new Decimal(50)
    } else {
      //省外
      extraStandard = new Decimal(80)
    }
    const extraCost = tabs.value[1].data.find((item: any) => item.item == '公杂费')
    //交通方式为单位派车、租车的子集则不允许报公杂费 自驾补助不为零不报公杂
    if (
        extraCost &&
        !form.value.trnp.every(target => ['6', '4'].includes(target)) &&
        /*props.details?.formData.food != '1' &&
        props.details?.formData.stay != '1' &&*/
        !selfDrive.amt
    ) {
      // extraCost.daysOrKilor = new Decimal(dayDiff).mul(new Decimal(psnCount))
      // 不与出差天数挂钩，默认单人只算往返 2
      extraCost.daysOrKilor = new Decimal(2).mul(new Decimal(psnCount))
      extraCost.std = extraStandard.toNumber()
      extraCost.amt = extraCost.daysOrKilor.mul(extraStandard).toNumber()
      const idx = tabs.value[1].data.indexOf(extraCost)
      loadSummary('amt', idx, 'b', extraCost.amt)
    }

    //刷新汇总
    changeSummary()
  })
}

const hasDuplicates = (data: any[], key: string) => {
  const seen = new Set()

  for (const item of data) {
    if (item[key]) {
      const valuesArray = item[key].split(',')
      for (const value of valuesArray) {
        const trimValue = value.trim()
        if (seen.has(trimValue)) {
          return true
        }
        seen.add(trimValue)
      }
    }
  }
  return false
}

const loadSummary = (key: string, idx: number, prefix: string, val: number) => {
  let rk = prefix + key + idx
  let cost = costs.value.find(c => c.key == rk)
  if (cost) {
    cost.val = val
  } else {
    costs.value.push({
      key: rk,
      val,
    })
  }
}

const getStandard = (addrCode: number) => {
  if (!addrCode) return
  let curAddrCode = addrCode
  while (true) {
    let standardItem = travelAccomStandardList.value.find((item: any) => item.evectionAddr == curAddrCode)
    if (standardItem) {
      return standardItem
    }
    if (!addressData.value) return
    let addrItem: any = addressData.value.find((item: any) => item.id == curAddrCode)
    if (addrItem && addrItem.parentId != 192) {
      curAddrCode = addrItem.parentId
      continue
    }
    return
  }
}

const getAddrLayer = (addrCode: number) => {
  let addrLayer = <any>[]
  let curAddrCode = addrCode
  if (!addressData.value) return addrLayer
  while (true) {
    let addrItem: any = addressData.value.find((item: any) => item.id == curAddrCode)
    if (addrItem) {
      addrLayer.unshift(addrItem.id)
      if (addrItem.parentId != 192) {
        curAddrCode = addrItem.parentId
        continue
      }
    }
    return addrLayer
  }
}

//多个项目选择同一发票
const batchChoose = () => {
  if (batchChoosedKeys.value.length == 0) {
    window.$message.warning('当前未选择项目')
    return
  }
  invoBatchChoosed.value = true
  showInvoChoosePane.value = true
}

//费用报销、合同、零星采购、工资计算项目明细
const calItemDetails = (details: any) => {
  let dcVal = new Decimal(0)
  details.forEach((d: any, idx: number) => {
    d.typeDetail = {type: d.type, budgetCode: d.budgetCode}
    d.index = idx + 1
    dcVal = dcVal.plus(new Decimal(d.amt ?? 0))
    //sumVal1.value += d.amt ?? 0
    costs.value.push({
      key: 'jamt' + idx,
      val: d.amt ?? 0,
    })
  })
  sumVal1.value = dcVal.toNumber()
  reimTable.value.data = details

  changeSummary()
}

const getDocPsn = (obj: any, index?: number) => {
  let psnNameArr = []
  let totalCount = 0
  
  if (typeof index === 'number' && addTravellerRef.value && Array.isArray(addTravellerRef.value)) {
    // 多行程情况
    if (addTravellerRef.value[index]?.busTravellerData) {
      psnNameArr = addTravellerRef.value[index].busTravellerData
        .filter((item: any, idx: number) => idx < 20)
        .map((item: any) => item.tripPsnName)
      totalCount = addTravellerRef.value[index].busTravellerData.length
    }
  } else if (addTravellerRef.value?.busTravellerData) {
    // 单行程情况
    psnNameArr = addTravellerRef.value.busTravellerData
      .filter((item: any, idx: number) => idx < 20)
      .map((item: any) => item.tripPsnName)
    totalCount = addTravellerRef.value.busTravellerData.length
  }
  
  obj.attendNames = psnNameArr.join(',')
  if (totalCount > 20) {
    obj.attendNames = obj.attendNames + '等' + totalCount + '人'
  }
  obj.attendNums = totalCount
}

const getDocItems = (index?: number) => {
  let items = []
  const tabData = tabs.value[0].data
  if (tabData) {
    tabData.forEach((item: any) => {
      items.push({
        itemName: item.item,
        docNum: item.docNum,
        itemAmt: item.amt,
      })
    })
  }
  return items
}

const getDocSubItems = (index?: number) => {
  let subItems = []
  const tabData = tabs.value[1].data
  if (tabData) {
    tabData.forEach((si: any) => {
      subItems.push({
        subItemName: si.item,
        daysOrKilor: si.daysOrKilor,
        std: si.std,
        subItemAmt: si.amt,
      })
    })
  }
  return subItems
}

const getDocReimItems = async (index?: number) => {
  let items = []
  let orgInfo = await queryOrg({activeFlag: '1', orgTypes: []})
  let flatEconFun = JPGlobal.flattenTree(econFunOptions.value)
  
  const reimData = reimTable.value.data
  if (reimData) {
    reimData.forEach((ri: any) => {
      items.push({
        index: ri.index,
        reimAbst: ri.reimAbst,
        deptCode: ri.deptCode,
        deptName: orgInfo.data.find((item: any) => item.orgId == ri.deptCode)?.orgName ?? '',
        type: ri.type,
        typeName: flatEconFun.find((item: any) => item.value == ri.type)?.label ?? '',
        itemAmt: ri.amt,
      })
    })
  }
  return items
}

//是否时交通费定额补助的区域
const isTrafficeNebor = (): boolean => {
  const evectionAddr = form.value.evectionAddr
  //临近地区
  //成都市     510100
  //德阳市     510600
  //广汉市     510681
  //什邡市     510682
  //绵竹市     510683
  //德阳市，罗江区 510604

  let trafficAddr = [510100, 510600, 510681, 510682, 510683, 510604]
  const addrLayerArr = getAddrLayer(evectionAddr)

  if (
      (addrLayerArr[2] && trafficAddr.includes(addrLayerArr[2])) ||
      (addrLayerArr[1] && trafficAddr.includes(addrLayerArr[1]))
  )
    return true
  return false
}

const addCameraFile = (cameraList: UploadFileInfo[]) => {
  fileList.value.push(...cameraList)
}

//差旅、培训获取项目附件数量
const getDocItemsAttNums = (index?: number) => {
  let total = 0
  const tabData0 = tabs.value[0].data
  const tabData1 = tabs.value[1].data
  
  if (tabData0) {
    tabData0.forEach((item: any) => {
      if (item.att) total += item.att.split(',').length
    })
  }
  if (tabData1) {
    tabData1.forEach((item: any) => {
      if (item.att) total += item.att.split(',').length
    })
  }
  return total
}

const doPurmChoose = () => {
}

//费用报销获取项目附件数量
const getDocReimItemsAttNums = (index?: number) => {
  let total = 0
  const reimData = reimTable.value.data
  if (reimData) {
    reimData.forEach((ri: any) => {
      if (ri.att) total += ri.att.split(',').length
    })
  }
  return total
}

const getDocAuditSteps = () => {
  let auditDetails = []
  props.tasks
      .filter((item: any) => {
        //整个流程可能会出现退回的环节，且相同环节会多次通过，筛选出这些环节
        if ((!item.propertyMap || !item.propertyMap?.cashAudit) && item.status != '5') {
          return true
        }
      })
      .sort((a, b) => b.createTime - a.createTime) //排倒序
      .forEach((item: any) => {
        let auditNode = {
          title: item.name,
          remark: item.reason ?? '',
          chkTime: formatDate(item.auditTime ?? item.endTime),
          sign: item.signUrl,
          taskDefinitionKey: item.taskDefinitionKey,
          createTime: item.createTime,
        }
        auditDetails.push(auditNode)
      })
  let trueDetails = []
  const seen = new Set()
  auditDetails.forEach((item: any, idx: number) => {
    if (!seen.has(item.taskDefinitionKey)) {
      seen.add(item.taskDefinitionKey)
      trueDetails.push(item)
    }
  })

  trueDetails.sort((a, b) => a.createTime - b.createTime) //排正序
  return trueDetails
}

const printDoc = async (docReimType: string) => {
  // 检测是否为多行程 - 通过检查是否有多个报销记录
  const isMultiTrip = Array.isArray(props.details) || 
    (props.details && Array.isArray(props.details.formData)) ||
    (typeof props.id === 'string' && props.id.includes(','))

  if (isMultiTrip) {
    // 多行程处理逻辑
    const docDatas = []
    
    // 确定行程数量和数据源
    let tripCount = 1
    let detailsArray = []
    let formsArray = []
    
    if (Array.isArray(props.details)) {
      tripCount = props.details.length
      detailsArray = props.details
      // 从props.details中提取formData
      formsArray = props.details.map(detail => detail.formData || detail)
    } else if (props.details && Array.isArray(props.details.formData)) {
      tripCount = props.details.formData.length
      formsArray = props.details.formData
      // 创建对应的details数组
      detailsArray = new Array(tripCount).fill(props.details)
    } else if (typeof props.id === 'string' && props.id.includes(',')) {
      const ids = props.id.split(',')
      tripCount = ids.length
      // 使用当前数据作为每个行程的数据（这种情况下可能需要从后端重新获取数据）
      formsArray = new Array(tripCount).fill(form.value)
      detailsArray = new Array(tripCount).fill(details.value)
    }
    
    for (let i = 0; i < tripCount; i++) {
      const docData = {
        id: null,
        reimType: '',
        appyerTime: '',
        appyerName: '',
        evectionBegnTime: '',
        evectionEndTime: '',
        deptName: '',
        evectionRea: '',
        evectionAddr: '',
        bank: '',
        acctname: '',
        bankcode: '',
        attendNames: '',
        attendNums: 0,
        items: [],
        reimItems: [],
        attNums: 0,
        subItems: [],
        sum: 0,
        capSum: '零',
        auditSteps: [],
      }

      // 为每个行程构建数据
      if (docReimType == '1' || docReimType == '2') {
        // 差旅/培训多行程处理
        const currentForm = formsArray[i]
        const currentDetails = detailsArray[i]
        
        docData.id = currentForm.id
        docData.reimType = docReimType
        docData.appyerTime = currentForm.appyerTime?.substring(0, 10) ?? ''
        docData.appyerName = currentForm.appyerName || currentForm.appyer
        docData.evectionBegnTime = currentForm.evectionBegnTime
        docData.evectionEndTime = currentForm.evectionEndTime
        
        await queryOrg({activeFlag: '1', orgTypes: []}).then((res: any) => {
          docData.deptName = res.data.find((item: any) => item.orgId == currentForm.appyerDept)?.orgName ?? ''
        })
        
        docData.evectionRea = currentForm.evectionRea
        docData.evectionAddr = JPGlobal.findPath(currentForm.evectionAddr, addressData.value!)
        
        // 收款信息
        docData.bank = currentForm.bank
        docData.acctname = currentForm.acctname
        docData.bankcode = currentForm.bankcode
        

        
        // 随行人员 - 构建当前行程的人员信息
        const currentPsnDetails = currentDetails.psnDetails || []
        let psnNameArr = currentPsnDetails
          .filter((item: any, idx: number) => idx < 20)
          .map((item: any) => item.tripPsnName || item.empName)
        docData.attendNames = psnNameArr.join(',')
        if (currentPsnDetails.length > 20) {
          docData.attendNames = docData.attendNames + '等' + currentPsnDetails.length + '人'
        }
        docData.attendNums = currentPsnDetails.length
        
        // 项目和补助项目 - 使用当前行程的数据
        const currentItems = currentDetails.itemDetails || []
        const currentSubItems = currentDetails.subsItemDetails || []
        
        docData.items = currentItems.map((item: any) => ({
          itemName: item.item,
          docNum: item.docNum,
          itemAmt: item.amt,
        }))
        
        docData.subItems = currentSubItems.map((subItem: any) => ({
          subItemName: subItem.item,
          daysOrKilor: subItem.daysOrKilor,
          std: subItem.std,
          subItemAmt: subItem.amt,
        }))

        // 计算当前行程的总金额 (项目 + 补助项目)
        let currentSum = 0
        // 计算项目金额
        docData.items.forEach((item: any) => {
          if (item.itemAmt) currentSum += parseFloat(item.itemAmt)
        })
        // 计算补助项目金额
        docData.subItems.forEach((subItem: any) => {
          if (subItem.subItemAmt) currentSum += parseFloat(subItem.subItemAmt)
        })
        
        docData.sum = currentSum
        docData.capSum = JPGlobal.numberToChineseCapital(currentSum)
        
        // 附件数量
        let itemsAttNums = 0
        currentItems.forEach((item: any) => {
          if (item.att) itemsAttNums += item.att.split(',').length
        })
        currentSubItems.forEach((item: any) => {
          if (item.att) itemsAttNums += item.att.split(',').length
        })
        
        docData.attNums = 
          itemsAttNums +
          (currentDetails.fileRecords?.length || 0) +
          (currentForm.att ? currentForm.att.split(',').length : 0)
      } else if (
        docReimType == '3' ||
        docReimType == '6' ||
        docReimType == '8' ||
        docReimType == '10' ||
        docReimType == '11' ||
        docReimType == '13'
      ) {
        // 其他类型多行程处理
        const currentForm = formsArray[i]
        const currentDetails = detailsArray[i]
        
        docData.id = currentForm.id
        docData.reimType = docReimType
        docData.appyerTime = currentForm.appyerTime?.substring(0, 10) ?? ''
        docData.appyerName = currentForm.appyerName || currentForm.appyer
        
        await queryOrg({activeFlag: '1', orgTypes: []}).then((res: any) => {
          docData.deptName = res.data.find((item: any) => item.orgId == currentForm.appyerDept)?.orgName ?? ''
        })
        
        // 报销详情 - 使用当前行程的报销项数据
        const currentReimItems = currentDetails.itemDetails || []
        let orgInfo = await queryOrg({activeFlag: '1', orgTypes: []})
        let flatEconFun = JPGlobal.flattenTree(econFunOptions.value)
        
        docData.reimItems = currentReimItems.map((ri: any) => ({
          index: ri.index,
          reimAbst: ri.reimAbst,
          deptCode: ri.deptCode,
          deptName: orgInfo.data.find((item: any) => item.orgId == ri.deptCode)?.orgName ?? '',
          type: ri.type,
          typeName: flatEconFun.find((item: any) => item.value == ri.type)?.label ?? '',
          itemAmt: ri.amt,
        }))
        
        // 计算当前行程的总金额 (报销项金额总和)
        let currentSum = 0
        // 使用处理后的reimItems数组计算总金额
        docData.reimItems.forEach((ri: any) => {
          if (ri.itemAmt) currentSum += parseFloat(ri.itemAmt)
        })
        
        docData.sum = currentSum
        docData.capSum = JPGlobal.numberToChineseCapital(currentSum)
        
        // 附件数量
        let reimItemsAttNums = 0
        currentReimItems.forEach((ri: any) => {
          if (ri.att) reimItemsAttNums += ri.att.split(',').length
        })
        
        docData.attNums = 
          reimItemsAttNums +
          (currentDetails.fileRecords?.length || 0) +
          (currentForm.att ? currentForm.att.split(',').length : 0)
        
        // 收款信息
        docData.bank = currentForm.bank
        docData.acctname = currentForm.acctname
        docData.bankcode = currentForm.bankcode
      }
      
      // 审核流程 - 使用通用的审核流程（所有行程共享）
      docData.auditSteps = getDocAuditSteps()
      
      // 将报销涉及到的金额转为字符串
      docData.sum = String(docData.sum)
      if (docData.reimItems && Array.isArray(docData.reimItems)) {
        docData.reimItems.forEach(item => {
          if (item.itemAmt !== undefined && item.itemAmt !== null) {
            item.itemAmt = String(item.itemAmt)
          }
        })
      }
      
      docDatas.push(docData)
    }
    
    // 调用多行程接口
    queryEcsReimDocMultiTrip(docDatas).then((res: any) => {
      if (res.code == 200) {
        bucket.value = 'temp'
        ossInfo.value.name = res.data.fileName
        ossInfo.value.path = res.data.filePath
        showPreview.value = true
      }
    })
  } else {
    // 原有的单行程处理逻辑
    const docData = {
      id: null,
      reimType: '',
      appyerTime: '',
      appyerName: '',
      evectionBegnTime: '',
      evectionEndTime: '',
      deptName: '',
      evectionRea: '',
      evectionAddr: '',
      bank: '',
      acctname: '',
      bankcode: '',
      attendNames: '',
      attendNums: 0,
      items: [],
      reimItems: [],
      attNums: 0,
      subItems: [],
      sum: 0,
      capSum: '零',
      auditSteps: [],
    }
    
    if (docReimType == '1' || docReimType == '2') {
      //报账日期，暂为申请时间
      docData.id = form.value.id
      docData.reimType = docReimType
      docData.appyerTime = form.value.appyerTime.substring(0, 10) ?? ''
      docData.appyerName = form.value.appyerName
      docData.evectionBegnTime = form.value.evectionBegnTime
      docData.evectionEndTime = form.value.evectionEndTime
      await queryOrg({activeFlag: '1', orgTypes: []}).then((res: IRes) => {
        docData.deptName = res.data.find((item: any) => item.orgId == form.value.appyerDept)?.orgName ?? ''
      })
      docData.evectionRea = form.value.evectionRea
      docData.evectionAddr = JPGlobal.findPath(form.value.evectionAddr, addressData.value!)

      //收款信息
      docData.bank = form.value.bank
      docData.acctname = form.value.acctname
      docData.bankcode = form.value.bankcode
      docData.sum = form.value.sum
      docData.capSum = form.value.capSum
      //随行人员
      getDocPsn(docData)
      //項目
      docData.items = getDocItems()
      //輔助項目
      docData.subItems = getDocSubItems()
      //附件数量
      docData.attNums =
          getDocItemsAttNums() +
          details.value.fileRecords.length +
          (form.value.att ? form.value.att.split(',').length : 0)
      //审核流程
      docData.auditSteps = getDocAuditSteps()
    } else if (
        docReimType == '3' ||
        docReimType == '6' ||
        docReimType == '8' ||
        docReimType == '10' ||
        docReimType == '11' ||
        docReimType == '13'
    ) {
      docData.id = form.value.id
      docData.reimType = docReimType
      //报账日期，暂为申请时间
      docData.appyerTime = form.value.appyerTime.substring(0, 10) ?? ''
      docData.appyerName = form.value.appyerName
      //科室
      await queryOrg({activeFlag: '1', orgTypes: []}).then((res: IRes) => {
        docData.deptName = res.data.find((item: any) => item.orgId == form.value.appyerDept)?.orgName ?? ''
      })
      //报销详情
      docData.reimItems = await getDocReimItems()
      //金额
      docData.sum = form.value.sum
      docData.capSum = form.value.capSum
      //附件数量
      docData.attNums =
          getDocReimItemsAttNums() +
          details.value.fileRecords.length +
          (form.value.att ? form.value.att.split(',').length : 0)
      //收款信息
      docData.bank = form.value.bank
      docData.acctname = form.value.acctname
      docData.bankcode = form.value.bankcode
      //审核流程
      docData.auditSteps = getDocAuditSteps()
    } else if (docReimType == '4') {
      docData.id = form.value.id
      docData.reimType = docReimType
      //报账日期，暂为申请时间
      docData.appyerTime = form.value.appyerTime.substring(0, 10) ?? ''
      docData.appyerName = form.value.appyerName
      //科室
      await queryOrg({activeFlag: '1', orgTypes: []}).then((res: IRes) => {
        docData.deptName = res.data.find((item: any) => item.orgId == form.value.appyerDept)?.orgName ?? ''
      })
      //报销详情
      let shareTypes = JPGlobal.getDictByType('ACTIG_SHARE_TYPE')
      let typeName = shareTypes.find((item: any) => item.value == form.value.shareType)?.label
      let item = [
        {
          index: 1,
          reimAbst: form.value.evectionRea,
          deptCode: form.value.appyerDept,
          deptName: docData.deptName,
          typeName: typeName,
          itemAmt: form.value.sum,
        },
      ]
      //附件张数
      docData.attNums = details.value.fileRecords.length + (form.value.att ? form.value.att.split(',').length : 0)
      docData.reimItems = item
      //金额
      docData.sum = form.value.sum
      docData.capSum = form.value.capSum
      //收款信息
      docData.bank = form.value.bank
      docData.acctname = form.value.acctname
      docData.bankcode = form.value.bankcode
      //审核流程
      docData.auditSteps = getDocAuditSteps()
    } else {
    }

    // 将报销涉及到的金额转为字符串
    docData.sum = String(docData.sum)
    if (docData.reimItems && Array.isArray(docData.reimItems)) {
      docData.reimItems.forEach(item => {
        if (item.itemAmt !== undefined && item.itemAmt !== null) {
          item.itemAmt = String(item.itemAmt)
        }
      })
    }

    queryEcsReimDoc(docData).then((res: any) => {
      if (res.code == 200) {
        bucket.value = 'temp'
        ossInfo.value.name = res.data.fileName
        ossInfo.value.path = res.data.filePath
        showPreview.value = true
      }
    })
  }
}

//展示差旅审批表
const showApprInfo = () => {
  let param = {
    pageSize: 20,
    pageNum: 1,
    id: form.value.travelApprId,
  }
  queryEcsReimTravelApprNew(param).then((res: any) => {
    currentTravelProcessInstanceId.value = res.data.records[0].processInstanceId
    showTravel.value = true
  })
}

watch(
    () => trainAmt,
    amt => {
      addTravellerRef.value.apportionAmt(amt, narrowTrainAmt)
    }
)

watch(
    () => ZCFAmt,
    amt => {
      addTravellerRef.value.apportionAmt(trainAmt, narrowTrainAmt, amt)
    }
)

onMounted(() => {
  if (props.setHandleAuditBefoerFn) {
    props.setHandleAuditBefoerFn(auditConfirmBefore)
  }
  // init()

  // 查询经济科目
  queryEcsEconFunSubCfg({status: '2', activeFlag: '1', notPaging: true}).then((res: IPageRes) => {
    econFunOptions.value = res.data as any
  })
  //查询固定辅助项
  queryEcsReimFixedAsst({itemType: '2'}).then((res: any) => {
    reimFixAsstDetails.value = res.data
  })
  //查询科室对应映射关系
  queryHrmOrgAgencyMapNoPage({pageNumpageSize: 20}).then((res: any) => {
    orgAgencyMap.value = res.data
  })
  //查询往来单位信息
  queryEcsCorrsInsCfg({qs: '', activeFlag: '', status: ''}).then((res: any) => {
    relCoCodeOptions.value = res.data
  })
  //查询科室信息
  queryOrg({activeFlag: '1', orgTypes: []}).then((res: IRes) => {
    deptInfos.value = res.data
    deptInfoOptions.value = JPGlobal.getTreeNode(deptInfos.value, 'orgId', 'orgParentId')
  })
  //查询科研项目信息
  queryRmsProjectMainInfo({queryRange: '2', briefAuditStatus: '1'}).then((res: any) => {
    if (res.code == 200) {
      res.data.forEach(e =>
          projectOptoins.value.push({
            value: e.id,
            label: e.projectName,
          })
      )
      console.log('@@-projectInfo', projectOptoins.value)
    }
  })
  //查询经费类别
  queryRmsFundingBudgetCfg({}).then((res: any) => {
    if (res.code == 200) {
      fundingOptions.value = JPGlobal.getTreeNode(res.data, 'id', 'parentId', 'fundingName')
      console.log('@@-fundingOptions', fundingOptions.value)
    }
  })
})

watch(
    () => sysStore.$state.initOnload,
    (newVal: boolean) => {
      init()
    }
)

watch(
    () => props.id,
    async id => {
      //查询地区信息
      await queryDictData({codeType: 'REGION'}).then(res => {
        addressData.value = res.data
      })
      if (id) {
        if (props.resubmit) {
          //如果是重新编辑，formData信息从页面来
          Object.assign(form.value, props.details.formData)
          onlyView.value = false
          //处理交通方式
          if (form.value.trnp) {
            form.value.trnp = form.value.trnp.split(',')
          }
          if (form.value.att && form.value.attName) {
            const generateRandomId = () => {
              const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
              let id = ''
              for (let i = 0; i < 8; i++) {
                id += characters.charAt(Math.floor(Math.random() * characters.length))
              }
              return id
            }

            let urls: string[] = []
            let files = []
            let splitAtt = form.value.att.split(',')
            let splitAttName = form.value.attName.split(',')
            const batchId = generateRandomId()

            // 创建一个数组来存储所有的 Promise
            let promises = []

            for (let i = 0; i < splitAtt.length; i++) {
              // 获取外链
              const promise = getSourceFileUrlOutsideChain({path: splitAtt[i], bucket: 'ecs'}).then((res: IRes) => {
                urls.push(res.data)

                return JPGlobal.downloadFileAndConvert(res.data, splitAttName[i]).then(res => {
                  files.push({
                    id: generateRandomId(),
                    batchId: batchId, // 使用相同的 batchId
                    file: res,
                    name: res.name,
                    fullPath: '/' + res.name,
                    status: 'pending',
                    type: res.type,
                  })
                })
              })

              // 将每个 Promise 添加到 promises 数组中
              promises.push(promise)
            }

            // 等待所有的 Promise 完成
            Promise.all(promises)
                .then(() => {
                  fileList.value.push(...files)
                })
                .catch(error => {
                  console.error('Error processing files:', error)
                })
            //清除att 和attName  防止不传文件时，依然设置为上次的附件
            delete form.value.att
            delete form.value.attName
          }
        } else {
          //查看报销或者审核报销
          const formDatas = await queryEcsReimDetailNew({
            // audit: props.isAudit,
            pageSize: 20,
            pageNum: 1,
            id: props.id,
          })
          const curRecord = formDatas.data.records[0]
          Object.assign(form.value, curRecord)
        }
        const itemDetailDatas = await queryItemDetail({id: id, attCode: form.value.attCode})
        details.value.formData = form.value
        details.value.itemDetails = itemDetailDatas.data.itemDetails
        details.value.subsItemDetails = itemDetailDatas.data.subsItemDetails
        details.value.psnDetails = itemDetailDatas.data.psnDetails
        details.value.reimAsstDetails = itemDetailDatas.data.reimAsstDetails
        details.value.fileRecords = itemDetailDatas.data.fileRecords

        const sortByDict = (data: any, dictType: string) => {
          // 排序
          let items: SysDict[] = getDictListByType(dictType)
          data.forEach((it: any) => {
            let dictItem = items.find(t => t.label == it.item)
            if (dictItem) it.sort = dictItem.sort
          })
          data.sort((a: any, b: any) => a.sort - b.sort)
          return data
        }
        let tf = form.value
        curType.value = form.value.type
        form.value.empCode = tf.appyer
        form.value.appyer = tf.appyerName
        //附件
        if (details.value.fileRecords) {
          let payFiles = details.value.fileRecords.filter(item => item.type == '2')
          payFiles.forEach((rcd: any) => {
            ossPayInfo.value.push({
              name: rcd.attName,
              path: rcd.att,
            })
          })
        }

        //回显表格数据
        if (curType.value == '1' || curType.value == '2') {
          tabs.value[0].data = sortByDict(
              details.value.itemDetails,
              curType.value == '1' ? 'EVECTION_ITEM' : 'EVECTION_ITEM_JXJY'
          )
          tabs.value[1].data = sortByDict(
              details.value.subsItemDetails,
              curType.value == '1' ? 'EVECTION_SUBS_ITEM' : 'EVECTION_SUBS_ITEM_JXJY'
          )
          if (tf.att && tf.attName) {
            let splitAtt = tf.att.split(',')
            let splitAttname = tf.attName.split(',')
            ossOtherInfo.value = []
            for (let i = 0; i < splitAtt.length; i++) {
              ossOtherInfo.value.push({
                name: splitAttname[i],
                path: splitAtt[i],
              })
            }
          }
        } else if (
            curType.value == '3' ||
            curType.value == '5' ||
            curType.value == '6' ||
            curType.value == '8' ||
            curType.value == '10' ||
            curType.value == '11' ||
            curType.value == '13'
        ) {
          if (tf.att && tf.attName) {
            let splitAtt = tf.att.split(',')
            let splitAttname = tf.attName.split(',')
            ossOtherInfo.value = []
            for (let i = 0; i < splitAtt.length; i++) {
              ossOtherInfo.value.push({
                name: splitAttname[i],
                path: splitAtt[i],
              })
            }
          }
          calItemDetails(details.value.itemDetails)
        } else if (curType.value == '4') {
          let fileRecords = details.value.fileRecords.filter((fr: any) => fr.type == '1')
          fileRecords.forEach((item: any) => {
            ossShareInfo.value.push({
              path: item.att,
              name: item.attName,
            })
          })
          let rk = 'jamt0'
          costs.value.push({
            key: rk,
            val: form.value.shareAmt,
          })
        }
        nextTick(() => {
          if (curType.value == '1' || curType.value == '2') {
            if (addTravellerRef.value) {
              addTravellerRef.value.setBusTravellerData(details.value.psnDetails)
            }
            deptChangeP()
          }
        })
      } else {
        //新增报销
        details.value.formData = props.details?.formData
        curType.value = props.type
        onlyView.value = props.view
      }
      formItems.value[0].formItems = formItemsTemp.value
      formItems.value[1].formItems = formItemsTemp2.value
      buildRules()
      showForm.value = true
      await init()
      pageLoaded.value = false

      //当前是否为票据审核或者出纳审核环节
      isInvoAudit.value = props.runningTasks && props.runningTasks[0]?.propertyMap?.invoAudit == '1'
      isCashAudit.value = props.runningTasks && props.runningTasks[0]?.propertyMap?.cashAudit == '1'
      //当前环节是否财务负责人审核
      isCWLeader.value = props.runningTasks && props.runningTasks[0]?.propertyMap?.cwLeader == '1'
      //当前是否为票据(piaoju)审核节点
      isPiaojuAudit.value = props.runningTasks && props.runningTasks[0]?.taskDefinitionKey === 'piaoju'
      
      // 在piaoju审核节点时，将已有附件加载到fileList中显示
      if (isPiaojuAudit.value  && ossOtherInfo.value.length > 0) {
        const generateRandomId = () => {
          const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
          let id = ''
          for (let i = 0; i < 8; i++) {
            id += characters.charAt(Math.floor(Math.random() * characters.length))
          }
          return id
        }

        const batchId = generateRandomId()
        const promises = []

        for (let i = 0; i < ossOtherInfo.value.length; i++) {
          const item = ossOtherInfo.value[i]
          // 获取外链并转换为File对象
          const promise = getSourceFileUrlOutsideChain({path: item.path, bucket: 'ecs'}).then((res: any) => {
            return JPGlobal.downloadFileAndConvert(res.data, item.name).then(file => {
              return {
                id: generateRandomId(),
                batchId: batchId,
                file: file,
                name: file.name,
                fullPath: '/' + file.name,
                status: 'finished',
                type: file.type,
              }
            })
          })
          promises.push(promise)
        }

        // 等待所有已有附件转换完成后添加到fileList
        Promise.all(promises)
          .then((files) => {
            fileList.value.push(...files)
            console.log('piaoju节点已加载已有附件到fileList:', files.length)
          })
          .catch(error => {
            console.error('加载已有附件失败:', error)
          })
      }
      
      formItems.value[0].formItems.forEach(item => {
        if (item.key === 'fundType') {
          item.disabled = !isCashAudit?.value ?? true // 这里替换成你想要的值
        }
        // 在piaoju节点时，允许修改申请信息表单（除了不可编辑字段）
        if (isPiaojuAudit.value && item.key !== 'empCode' && item.key !== 'appyer' && item.key !== 'id') {
          item.disabled = false
        }
      })
      if (props.isAudit && isInvoAudit.value && (curType.value == '1' || curType.value == '2')) {
        //差旅、培训票据审核节点可修改天数
        isApprAccountAudit.value = true
        tabs.value[0].data.forEach((item: any) => {
          // if (item.std) item.disabled = false
          item.disabled = false
        })
        tabs.value[1].data.forEach((item: any) => {
          // if (item.std) item.disabled = false
          item.disabled = false
        })
      }
      //除差旅、培训费、分摊费用，所有报销在财务负责人和财务会计环节可修改类别
      if (
          props.isAudit &&
          (isInvoAudit.value || isCWLeader.value) &&
          (curType.value == '3' ||
              curType.value == '6' ||
              curType.value == '8' ||
              curType.value == '10' ||
              curType.value == '11' ||
              curType.value == '12' ||
              curType.value == '13')
      ) {
        isOrdinaryAudit.value = true
        reimTable.value.data.forEach(item => {
          item.disabled = false
        })
      }
    },
    {immediate: true}
)

const tempSave = () => {
  let formData = new FormData()
  formRef.value.validate((errors: any) => {
    if (!errors) {
      form.value.appyer = form.value.empCode
      formData.append('type', curType.value!)
      //临时保存标志
      formData.append('tempSave', '1')
      // 添加表单数据
      for (let key in form.value) {
        if (!formData.has(key) && form.value[key]) {
          formData.append(key, (form.value as any)[key])
        }
      }
      // 添加项目
      if (['1', '2'].includes(curType.value)) {
        // 差旅费用和科教
        tabs.value.forEach(tab => {
          let prefix = tab.name == '1' ? 'itemDetails' : 'subsItemDetails'
          for (let i = 0; i < tab.data.length; i++) {
            let td = tab.data[i]
            for (let tdKey in td) {
              let pk = prefix + '[' + i + '].' + tdKey
              if (td[tdKey]) formData.append(pk, td[tdKey])
            }
          }
        })

        let busTravellerData = JSON.parse(JSON.stringify(addTravellerRef.value.busTravellerData))
        // 添加出差人
        for (let i = 0; i < busTravellerData.length; i++) {
          let td = busTravellerData[i]
          td.reimAmt = parseFloat(td.reimAmt ?? 0) + parseFloat(td.reimAmt2 ?? 0)
          for (let tdKey in td) {
            formData.append('psnDetails[' + i + '].' + tdKey, (td as any)[tdKey])
          }
        }
        //添加其他附件
        for (let i = 0; i < fileList.value.length; i++) {
          let f = fileList.value[i]
          formData.append('attFiles[' + i + ']', f.file as File)
        }
      } else if (
          curType.value == '3' ||
          curType.value == '5' ||
          curType.value == '6' ||
          curType.value == '8' ||
          curType.value == '10' ||
          curType.value == '11' ||
          curType.value == '13'
      ) {
        //添加其他附件
        for (let i = 0; i < fileList.value.length; i++) {
          let f = fileList.value[i]
          formData.append('attFiles[' + i + ']', f.file as File)
        }

        // 费用报销
        reimTable.value.data.forEach((item, idx) => {
          for (let itemKey in item) {
            let pk = 'itemDetails[' + idx + '].' + itemKey
            if (item[itemKey]) formData.append(pk, item[itemKey])
          }
        })
      } else if (curType.value == '4') {
        //添加文件
        for (let i = 0; i < fileList.value.length; i++) {
          let f = fileList.value[i]
          formData.append('attFiles[' + i + ']', f.file as File)
        }
      }

      for (var [a, b] of formData.entries()) {
        console.log(a, b)
      }

      // return
      emits('reimLoading', true)

      if (canSubmit.value) {
        canSubmit.value = false
        addEcsReimDetailNew(formData)
            .then(res => {
              window.$message.success('临时保存报销成功')
              emits('close')
              emits('reimLoading', false)
            })
            .catch(err => {
              emits('reimLoading', false)
            })
            .finally(() => {
              canSubmit.value = true
            })
      }
    }
  })
}

// 在 script setup 部分添加预览方法
const previewContractFile = () => {
  if (details.value?.formData.contractAtt) {
    ossInfo.value = {
      path: details.value.formData.contractAtt,
      name: details.value.formData.contractAttName,
    }
    bucket.value = 'cms' // 设置bucket为cms
    showPreview.value = true
  }
}

// 添加控制变量
const showContractPreview = ref(false)

// 添加切换方法
const toggleContractPreview = () => {
  showContractPreview.value = !showContractPreview.value
}

// 在 script setup 部分添加方法
const showContractDetail = () => {
  showDrawer.value = true
}
// 添加控制变量
const showDrawer = ref(false)

// 添加抽屉宽度计算
const drawerWidth = Math.floor(window.innerWidth * (2 / 3))
defineExpose({submit, auditConfirm, printDoc, tempSave})

//采购项目添加项
const purmChoose = () => {
  purmsChooseModal.value = true
}

const invoRecognInfo = ref({
  columns: [
    {
      title: '#',
      key: 'key',
      width: 50,
      render: (_, index) => {
        return `${index + 1}`
      },
    },
    {title: '备注', key: 'remarks', width: 300},
    {title: '销售方开户行', key: 'sellerBankAccountInfo', width: 100},
    {title: '受票方开户行', key: 'purchaserBankAccountInfo', width: 100},
    {title: '销售方名称', key: 'sellerName', width: 100},
    {title: '发票税额', key: 'invoiceTax', width: 100},
    {title: '大写金额', key: 'totalAmountInWords', width: 100},
    {title: '发票号码', key: 'invoiceNumber', width: 100},
    {title: '开票人', key: 'drawer', width: 100},
    {title: '开票日期', key: 'invoiceDate', width: 100},
    {title: '受票方税号', key: 'purchaserTaxNumber', width: 100},
    {title: '发票代码', key: 'invoiceCode', width: 100},
    {title: '受票方名称', key: 'purchaserName', width: 100},
    {title: '发票金额', key: 'totalAmount', width: 100},
    {title: '不含税金额', key: 'invoiceAmountPreTax', width: 100},
    {title: '销售方税号', key: 'sellerTaxNumber', width: 100},
  ],
  data: [],
})

//展示发票信息
const showInvoInfo = () => {
  const uniqueIds = new Set()
  reimTable.value.data.forEach(item => {
    if (item.invoId) {
      item.invoId.split(',').forEach(id => {
        uniqueIds.add(id)
      })
    }
  })
  if (uniqueIds.size == 0) {
    window.$message.warning('当前还未选择发票')
    return
  }
  // 查询发票信息
  queryEcsInvoRcdRecogn({ids: Array.from(uniqueIds)}).then((res: any) => {
    if (res.code == 200) {
      //获取发票中识别信息
      let recognInfo = []
      res.data.forEach(e => {
        if (e.idtfErrMsg) {
          let invoRecogn = JSON.parse(e.idtfErrMsg)
          //备注重新赋值
          if (e.chkData) {
            let chkData = JSON.parse(e.chkData)
            invoRecogn.remark = chkData.data.note
          }
          recognInfo.push(invoRecogn)
        }
      })
      invoRecognInfo.value.data = recognInfo
      showInvoRecognPane.value = true
    }
  })
}
</script>

<style scoped>
.exp-space {
  margin-top: 10px;
}

.des-content {
  /*width: 100%;*/
  /*position: absolute;*/
}
</style>
