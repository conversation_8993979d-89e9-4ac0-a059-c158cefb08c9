package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu2;

import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@LiteflowComponent(value = "SalaryVcrProviPsnTempDeductNodeZjrmyy", name = "工资发放-个人计提临时扣款-Zjrmyy")
public class SalaryVcrProviPsnTempDeductNodeZjrmyy extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);
        //获取待执行明细 taskDetails
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails();
        //获取凭证模版
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap().getOrDefault(ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_TEMP_DEDUCT, new ArrayList<>());
        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();
        //执行个人计提临时扣款逻辑
        for (SalaryVcrTaskDetialVo detail : taskDetails) {
            // 跳过金额为0的明细
            if (detail.getReimAmt() == null || detail.getReimAmt().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            // 匹配最佳模版
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, detail);
            if (matchedTemplate.isPresent()) {
                // 应用模版生成辅助项
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), detail, flowContext);
                insertAssts.addAll(detailAssts);
                log.debug("应用模版 {} 生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            } else {
                log.warn("未找到匹配的凭证模版，上下文: {}", detail);
                // 可以选择抛出异常或使用默认模版
                throw new RuntimeException("未找到匹配的凭证模版: " + detail.getReimName());
            }
        }
    }
}
