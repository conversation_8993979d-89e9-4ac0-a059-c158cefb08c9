package com.jp.med.erp.modules.liteflow.service;

import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import com.jp.med.erp.modules.vcrGen.vo.ExpenseFlowParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通用凭证流程服务接口
 * 基于LiteFlow实现各类凭证生成
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface VoucherFlowService {

    /**
     * 生成凭证辅助项
     *
     * @param param 凭证流程参数
     * @return 生成的辅助项列表
     */
    List<ErpReimAsstDto> generateVoucherAssts(ExpenseFlowParam param);

    /**
     * 获取凭证备注
     *
     * @param receiptVos
     * @param date
     * @param applyer
     * @param feeName
     * @return
     */
    String getAbs(List<ErpReimPayReceiptVo> receiptVos, String date, String applyer, String feeName);

    /**
     * 获取付款证明文件
     *
     * @param supType
     * @param reimId
     * @return
     */
    List<ErpReimPayReceiptVo> getPayReceiptInfos(String supType, Integer reimId);

} 