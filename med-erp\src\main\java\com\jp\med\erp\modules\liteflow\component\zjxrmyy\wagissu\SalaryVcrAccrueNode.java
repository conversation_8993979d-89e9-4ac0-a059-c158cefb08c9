package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@LiteflowComponent(value = "SalaryVcrAccrueNode", name = "工资计提凭证生成")
public class SalaryVcrAccrueNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Override
    public void process() throws Exception {
        log.info("开始执行工资计提凭证生成节点");
        
        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);
        
        // 获取工资计提任务详情 - 过滤工资计提类型的任务
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
            .stream()
            .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.SALARY))
            .collect(Collectors.toList());
            
        log.info("筛选出工资计提任务明细数量: {}", taskDetails.size());

        //获取凭证模版
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
            .getOrDefault(ErpConstants.BUSIVOUCHERTYPE_ACCRUE_WAG, new ArrayList<>());
        
        if (templates.isEmpty()) {
            log.warn("未找到工资计提凭证模版配置");
            throw new RuntimeException("未找到工资计提凭证模版配置");
        }
        
        log.info("找到工资计提凭证模版数量: {}", templates.size());
        
        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();
        
        //执行工资计提逻辑
        for (SalaryVcrTaskDetialVo detail : taskDetails) {
            // 跳过金额为0的明细
            if (detail.getReimAmt() == null || detail.getReimAmt().compareTo(BigDecimal.ZERO) == 0) {
                log.debug("跳过金额为0的明细: {}", detail.getReimName());
                continue;
            }

            // 跳过就业见习人员所有计提 EmpType = 696
            if (ErpConstants.EMP_TYPE_EMP_INSHIP.equals(detail.getEmpType())) {
                log.debug("跳过就业见习生活补贴的明细: {}", detail.getReimName());
                continue;
            }
            
            // 匹配最佳模版
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, detail);
            if (matchedTemplate.isPresent()) {
                // 应用模版生成辅助项
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), detail, flowContext);
                insertAssts.addAll(detailAssts);
                log.debug("应用模版 {} 生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            } else {
                log.warn("未找到匹配的凭证模版，上下文: {}", detail);
                throw new RuntimeException("未找到匹配的凭证模版: " + detail.getReimName());
            }
        }
        
        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);
        
        log.info("工资计提凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }
}
