package com.jp.med.erp.modules.liteflow.param;

import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 通用凭证流程参数
 * 用于封装各类凭证生成的输入参数
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class VoucherFlowParam {

    /**
     * 凭证业务分类
     * 1：费用报销 2：药品 3：工资 4：折旧
     */
    private String supType;
    
    /**
     * 凭证类型
     * 费用报销: 1-差旅 2-培训 3-其他费用 4-分摊费用 6-合同 8-采购 10-物资采购 11-其他费用(无发票) 12-往来支付 13-借款
     */
    private String type;
    
    /**
     * 业务凭证类型（用于LiteFlow流程识别）
     */
    private String busiVoucherType;
    
    /**
     * 报销ID列表
     */
    private List<Integer> ids;
    
    /**
     * 凭证日期
     */
    private String certificateDate;
    
    /**
     * 记录人员ID
     */
    private String recordPersonId;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 预置辅助项详情（用于模版匹配）
     */
    private List<ErpReimAsstDto> reimAsstDetails;
    
    /**
     * 付款方式 0-非现金 1-现金
     */
    private String payMethod;

    /**
     * 凭证号
     */
    private String vpzh;

    /**
     * 附件文件列表（用于无发票凭证）
     */
    private List<String> attFiles;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extParams;
    
    /**
     * 确定业务凭证类型
     */
    public String determineBusiVoucherType() {
        if (busiVoucherType != null) {
            return busiVoucherType;
        }
        
        // 根据supType和type确定业务凭证类型
        switch (supType) {
            case "1": // 费用报销
                return determineFeeVoucherType();
            case "2": // 药品
                return "drug_reim";
            case "4": // 折旧
                return "depreciation";
            default:
                throw new RuntimeException("不支持的凭证业务分类: " + supType);
        }
    }
    
    /**
     * 确定费用凭证类型
     */
    private String determineFeeVoucherType() {
        switch (type) {
            case "1": return "travel_reim";           // 差旅报销
            case "2": return "training_reim";         // 培训报销
            case "3": return "other_fee_reim";        // 其他费用
            case "4": return "allocation_fee_reim";   // 分摊费用
            case "6": return "contract_reim";         // 合同
            case "8": return "purchase_reim";         // 采购凭证
            case "10": return "material_purchase";    // 物资采购
            case "11": return "other_fee_no_invoice"; // 其他费用(无发票)
            case "12": return "transaction_payment";  // 往来支付
            case "13": return "loan_reim";           // 借款
            default:
                throw new RuntimeException("不支持的费用凭证类型: " + type);
        }
    }

} 