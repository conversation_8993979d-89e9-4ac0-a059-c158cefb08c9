package com.jp.med.erp.modules.liteflow.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jp.med.erp.modules.vcrGen.entity.ErpReimPsnDetail;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimPayReceiptVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class ExpenseVcrTaskDetialVo extends VcrFlowDetailBaseVo {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 报销任务id
     */
    @TableField("task_id")
    private Integer taskId;

    /**
     * 报销科室
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 报销类型 费用报销: 1-差旅 2-培训 3-其他费用 4-分摊费用 6-合同 8-采购 10-物资采购 11-其他费用(无发票) 12-往来支付 13-借款
     */
    @TableField("reim_type")
    private String reimType;

    /**
     * 报销金额
     */
    @TableField("reim_amt")
    private BigDecimal reimAmt;

    /**
     * 报销摘要
     */
    @TableField("reim_desc")
    private String reimDesc;

    /**
     * 报销项目
     */
    @TableField("reim_name")
    private String reimName;

    /**
     * 报销出差人数据
     */
    List<ErpReimPsnDetail> erpReimPsnDetails = new ArrayList<>();

    /**
     * 付款证明文件
     */
    List<ErpReimPayReceiptVo> erpReimPayReceiptVos = new ArrayList<>();

    /**
     * 冲抵报销原凭证信息
     */
    List<ErpReimAsstVo> erpReimAsstVos = new ArrayList<>();

    /**
     * 申请时间
     */
    private String appyerTime;

    /**
     * 是否冲抵借款(冲抵借款不需要上传付款证明文件)
     */
    private String isLoan;

    /**
     * 冲抵借款报销id
     */
    private Integer loanReimId;

    /**
     * 冲抵借款金额
     */
    private BigDecimal loanAmt;

    /**
     * 支付方式 1：现金
     */
    private String payMethod;

    @Override
    public String toString() {
        return "SalaryVcrTemplateTaskDetialVo{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", orgId='" + orgId + '\'' +
                ", reimType='" + reimType + '\'' +
                ", reimAmt=" + reimAmt +
                ", reimDesc='" + reimDesc + '\'' +
                ", reimName='" + reimName + '\'' +
                ", " + super.toString() +  // 合并父类属性
                '}';
    }
}
