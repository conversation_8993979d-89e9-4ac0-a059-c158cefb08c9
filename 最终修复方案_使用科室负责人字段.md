# 🔧 最终修复方案：使用科室负责人字段

## 📋 问题确认

✅ **数据库字段存在**: `cms_contract` 表中确实有 `use_org_person` 字段  
❌ **接口返回缺失**: API 返回的数据中没有 `useOrgPerson` 和 `useOrgPersonName` 字段  
🎯 **根本原因**: 后端 SQL 查询中没有查询该字段，VO 类中也缺少对应属性

## 🛠️ 修复步骤

### 步骤1: 修改 CmsContractVo.java

**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/vo/CmsContractVo.java`

在第47行 `private String useOrg;` 之后添加：

```java
/** 使用科室 */
private String useOrg;

/** 🆕 使用科室负责人 */
private String useOrgPerson;

/** 🆕 使用科室负责人姓名 */
private String useOrgPersonName;

/** 管理科室 */
private String manageOrg;
```

### 步骤2: 修改 CmsContractReadMapper.xml

**文件路径**: `med-cms/src/main/resources/mapper/contractManage/read/CmsContractReadMapper.xml`

#### 2.1 修改 SELECT 语句（第42行附近）

**查找这段代码**:
```xml
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, cco.opposite_name AS
oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, co.ct_type_code AS
ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

**修改为**:
```xml
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
co.use_org_person AS useOrgPerson, use_person_info.emp_name AS useOrgPersonName,
cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

#### 2.2 修改 FROM 语句（第62行附近）

**查找这段代码**:
```xml
FROM cms_contract co LEFT
JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code LEFT JOIN
hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN cms_contract_opposite cco ON
co.opposite_id = cco.id LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id =
coc.id LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

**修改为**:
```xml
FROM cms_contract co 
LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

## 🧪 验证步骤

### 1. 先检查数据库中的数据

执行以下 SQL 检查合同 336 的 `use_org_person` 字段：

```sql
-- 检查合同336的use_org_person字段
SELECT id, ct_name, use_org_person 
FROM cms_contract 
WHERE id = 336;

-- 检查所有合同的use_org_person字段使用情况
SELECT 
    COUNT(*) as total_contracts,
    COUNT(use_org_person) as has_use_org_person,
    COUNT(CASE WHEN use_org_person IS NOT NULL AND use_org_person != '' THEN 1 END) as has_non_empty_use_org_person
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0;

-- 测试关联查询
SELECT 
    co.id,
    co.ct_name,
    co.use_org_person,
    he.emp_name as use_org_person_name
FROM cms_contract co
LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code AND he.is_deleted = 0
WHERE co.id = 336;
```

### 2. 修改后端代码

按照上述步骤修改 `CmsContractVo.java` 和 `CmsContractReadMapper.xml` 文件。

### 3. 重启服务并测试

1. **重启后端服务**
2. **调用合同查询接口**
3. **检查返回数据是否包含**：
   ```json
   {
     "useOrgPerson": "员工代码",
     "useOrgPersonName": "员工姓名"
   }
   ```

## 🎯 预期结果

修复完成后，接口应该返回：

```json
{
  "id": 336,
  "ctName": "五金维修材料",
  "useOrg": "521001",
  "useOrgPerson": "0123",           // 🆕 新增
  "useOrgPersonName": "张三",        // 🆕 新增
  "manageOrg": "531001",
  "responsiblePerson": "0155",
  "responsiblePersonName": "彭心梅"
}
```

## 🚨 可能的情况

### 情况1: use_org_person 字段为空
如果数据库查询显示 `use_org_person` 为 NULL 或空字符串：
- 该合同确实没有设置使用科室负责人
- 需要在合同录入/编辑时补充该信息

### 情况2: 关联查询无结果
如果 `use_org_person` 有值但关联查询无结果：
- 员工代码在 `hrm_employee_info` 表中不存在
- 员工记录被标记为删除 (`is_deleted = 1`)

## 📋 关键修改点总结

1. **CmsContractVo.java**: 添加 `useOrgPerson` 和 `useOrgPersonName` 字段
2. **CmsContractReadMapper.xml**: 
   - SELECT 中添加 `co.use_org_person AS useOrgPerson, use_person_info.emp_name AS useOrgPersonName`
   - FROM 中添加 `LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code`

修改完成后，使用科室负责人就能正确显示了！
