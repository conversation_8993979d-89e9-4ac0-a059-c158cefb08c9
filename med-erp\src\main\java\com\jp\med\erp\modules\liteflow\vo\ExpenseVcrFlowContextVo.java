package com.jp.med.erp.modules.liteflow.vo;

import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 费用报销通用凭证流程上下文
 * 在LiteFlow流程组件间传递数据
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ExpenseVcrFlowContextVo extends VcrFlowContextBaseVo {

    /**
     * 报销ID列表
     */
    private List<Integer> reimIds;

    /**
     * 付款方式 0-非现金 1-现金
     */
    private String payMethod;

    /**
     * 凭证号
     */
    private String vpzh;

    /**
     * 附件文件列表（用于无发票凭证）
     */
    private List<String> attFiles;

    /**
     * 报销明细数据 liteflow
     */
    private List<ExpenseVcrTaskDetialVo> vcrTaskDetails;

    /**
     * 凭证业务报销分类 费用报销: 1-差旅 2-培训 3-其他费用 4-分摊费用 6-合同 8-采购 10-物资采购 11-其他费用(无发票) 12-往来支付 13-借款
     */
    private String reimType;

}