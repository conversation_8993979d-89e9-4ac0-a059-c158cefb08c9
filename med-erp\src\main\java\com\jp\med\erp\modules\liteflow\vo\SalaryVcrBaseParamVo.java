package com.jp.med.erp.modules.liteflow.vo;

import com.jp.med.common.vo.EmpEmployeeDictVo;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryRelCoConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工资凭证业务基础参数配置
 */
@Data
public class SalaryVcrBaseParamVo {

    /**
     * 科室映射对照
     */
    Map<String, HrmOrgAgencyMapVo> orgMappings;


    /**
     * 业务场景对应的凭证模版map
     */
    Map<String, List<VoucherTemplateDto>> voucheTmeplateDtoMap;

    /**
     * 员工类型数据
     */
    List<EmpEmployeeDictVo> empEmployeeDictVos;

    /**
     * 月度工资对应的任务类型与任务明细ID对照
     */
    Map<String, Integer> salaryTaskMap;

    /**
     * 凭证业务分类
     * 1：报销
     * 2：药品
     * 3：工资
     * 4：折旧
     */
    private String supType;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 医疗机构编码
     */
    String hospitalId;
    /**
     * 业务凭证类型
     */
    String busiVoucherType;
    /**
     * 工资任务类型
     */
    String salaryType;
    /**
     * 工资任务id
     */
    Integer taskId;
    /**
     * 工资任务批次ID
     */
    Integer salaryId;

    /**
     * 员工往来单位对照Map,多条情况取第一条
     */
    Map<String, List<ErpVcrSalaryRelCoConfigVo>> erpVcrSalaryRelCoConfigMap = new HashMap<>();

}
