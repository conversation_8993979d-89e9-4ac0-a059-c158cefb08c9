package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 企业缴纳四险两金凭证生成节点
 * 基于模版匹配方式生成企业缴纳相关的凭证辅助项
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrBusinessPaymentNode", name = "企业缴纳四险两金凭证生成")
public class SalaryVcrBusinessPaymentNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Override
    public void process() throws Exception {
        log.info("开始执行企业缴纳四险两金凭证生成节点");
        
        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);
        
        // 获取企业缴纳任务详情 - 过滤企业缴纳类型的任务
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
            .stream()
            .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.BUSINESS_PAYMENT))
            .collect(Collectors.toList());
            
        log.info("筛选出企业缴纳任务明细数量: {}", taskDetails.size());
        
        if (taskDetails.isEmpty()) {
            log.info("没有企业缴纳任务明细，跳过处理");
            return;
        }
            
        //获取凭证模版
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
            .getOrDefault(ErpConstants.BUSIVOUCHERTYPE_ACCRUE_ENTP_INSUR_GOLD, new ArrayList<>());
        
        if (templates.isEmpty()) {
            log.warn("未找到企业缴纳凭证模版配置");
            throw new RuntimeException("未找到企业缴纳凭证模版配置");
        }
        
        log.info("找到企业缴纳凭证模版数量: {}", templates.size());
        
        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();
        
        //执行企业缴纳逻辑
        for (SalaryVcrTaskDetialVo detail : taskDetails) {
            // 跳过金额为0的明细
            if (detail.getReimAmt() == null || detail.getReimAmt().compareTo(BigDecimal.ZERO) == 0) {
                log.debug("跳过金额为0的明细: {}", detail.getReimName());
                continue;
            }
            
            // 匹配最佳模版
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, detail);
            if (matchedTemplate.isPresent()) {
                // 应用模版生成辅助项
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), detail, flowContext);
                insertAssts.addAll(detailAssts);
                log.debug("应用模版 {} 生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            } else {
                log.warn("未找到匹配的凭证模版，上下文: 科室={}, 人员类型={}, 项目={}", 
                    detail.getOrgId(), detail.getEmpType(), detail.getReimName());
                throw new RuntimeException("未找到匹配的凭证模版: " + detail.getReimName());
            }
        }
        
        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);
        
        log.info("企业缴纳四险两金凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }
} 