package com.jp.med.erp.modules.vcrGen.constant;

import java.util.HashMap;
import java.util.Map;

public interface ErpConstants {

    //--------------------start凭证来源------------------
    /**
     * 报销
     **/
    String VCR_SOURCE_REIM = "1";
    /**
     * 药品报销
     **/
    String VCR_SOURCE_DRUG_REIM = "2";

    /**
     * 工资
     **/
    String VCR_SOURCE_SALARY = "3";

    /**
     * 折旧
     **/
    String VCR_SOURCE_DEPR = "4";
    //--------------------end凭证来源--------------------

    //----------------应发工资start----------------
    //岗位工资
    String POST_SALARY = "postSalary";
    // 薪级工资
    String SAL_GRADE_SALARY = "salGradeSalary";
    // 护士 10%
    String NURSE_SALARY = "nurseSalary";
    // 地区附加津贴
    String AREA_SALARY = "areaSalary";
    // 护龄津贴
    String AGE_SALARY = "ageSalary";
    // 基础性绩效
    String BASIC_PERF = "basicPerf";
    // 通讯费补贴
    String COMMUNICATION_FEE_ALLOWANCE = "communicationFeeAllowance";
    // 生活补贴
    String LIFE_SALARY = "lifeSalary";
    // 人力临时增加
    String TEMPORARY_ADD_SALARY = "temporaryAddSalary";
    // 财务临时增加
    String TEMPORARY_ADD_SALARY2 = "temporaryAddSalary2";

    //岗位工资
    String POST_SALARY_CODE = "3010101";
    // 薪级工资
    String SAL_GRADE_SALARY_CODE = "3010102";
    // 护士 10%
    String NURSE_SALARY_CODE = "3010103";
    // 地区附加津贴
    String AREA_SALARY_CODE = "3010201";
    // 护龄津贴
    String AGE_SALARY_CODE = "3010202";
    // 基础性绩效
    String BASIC_PERF_CODE = "3010705";
    // 生活补贴
    String LIFE_SALARY_CODE = "30305";

    //----------------应发工资end------------------


    //-----------------代扣代缴start---------------

    // 养老保险
    String PENSION_INSURANCE = "pensionInsurance";
    // 医疗保险
    String MEDICAL_INSURANCE = "medicalInsurance";
    // 失业保险
    String UNEMPLOYMENT_INSURANCE = "unemploymentInsurance";
    // 住房基金
    String HOUSING_FUND = "housingFund";
    // 职业年金
    String OCCUPATION_ANNUITY = "occupationalAnnuity";
    //个人所得税
    String PERSON_TAX = "personalIncomeTaxDeduction";

    //房租费
    String RENT = "rent";
    //水费
    String WATER_CHARGE = "waterCharge";
    // 人力临时扣款
    String TEMPORARY_REDUCE_SALARY = "temporaryReduceSalary";
    // 财务临时扣款
    String TEMPORARY_REDUCE_SALARY2 = "temporaryReduceSalary2";

    //银行存款 不是工资明细中的项，但是工资凭证会生成此项贷方，所以此项用于salaryConfig的配置
    String BANK_DEPOSIT = "bankDeposit";

    /**
     * 工资总额-应付职工薪酬-基本工资（含离退休费） 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SALARY_TOTAL_BASE = "salaryTotalBase";

    /**
     * 工资总额-应付职工薪酬-国家统一规定的津贴补贴 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SALARY_TOTAL_ALLOWANCE = "salaryTotalAllowance";

    /**
     * 工资总额-应付职工薪酬-规范津贴补贴（绩效工资） 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SALARY_TOTAL_PERFORM_SAL = "salaryTotalPerformSal";

    /**
     * 工资总额-冲账 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String OFFSET_ACCOUNT = "offsetAccount";

    /**
     * 工资总额-特殊往来账 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SPECIAL_CORRS_ACCOUNT = "specialCorrsAccount";

    /**
     * 工资总额-特殊单采血浆站 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SPECIAL_PLASMA_STATION = "specialPlasmaStation";

    /**
     * 工资总额-吴军兵特殊往来账 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SPECIAL_ACCOUNT_3077 = "specialAccount3077";


    /**
     * 工资总额-就业见习人员特殊往来账 非工资明细中项，但凭证会生成此项借，需在salaryConfig配置
     **/
    String SPECIAL_ACCOUNT_EMP_INSHIP = "specialAccountEmpInship";

    //-----------------代扣代缴end-----------------


    //------------------企业缴纳start--------------
    // 机关事业单位基本养老保险缴费
    String PENSION_INSURANCE_ENTP = "pensionInsuranceEntp";
    // 职工企业基本养老保险缴费
    String PENSION_INSURANCE_ENTP2 = "pensionInsuranceEntp2";
    // 职工基本医疗保险缴费
    String MEDICAL_INSURANCE_ENTP = "medicalInsuranceEntp";
    // 职工失业保险缴
    String UNEMPLOYMENT_INSURANCE_ENTP = "unemploymentInsuranceEntp";
    // 住房公积金
    String HOUSING_FUND_ENTP = "housingFundEntp";
    // 职业年金缴费
    String OCCUPATION_ANNUITY_ENTP = "occupationalAnnuityEntp";
    // 职工工伤保险缴费
    String INJR_INSU_ENTP = "injrInsuEntp";
    // 工会会费支出
    String PUB_FEE_ENTP = "pubFeeEntp";
    //------------------企业缴纳end----------------

    //--------------------工会会费代扣start-------------
    //工会会费
    String LABOR_UNION = "laborUnion";
    //--------------------工会会费代扣end-------------

    /**
     * 辅助信息-现金流量-信息代码
     **/
    String ACTIG_ASST_CASHFLOWCODE = "101";
    /**
     * 辅助信息-往来单位-信息代码
     **/
    String ACTIG_ASST_RELCOCODE = "103";

    /**
     * 辅助信息-资金性质-信息代码
     **/
    String ACTIG_ASST_FUNDTYPE = "104";

    /**
     * 辅助信息-项目-信息代码
     **/
    String ACTIG_ASST_PROJCODE = "105";

    /**
     * 辅助信息-部门-信息代码
     **/
    String ACTIG_ASST_CFG_DEPT = "106";

    /**
     * 辅助信息-功能科目-信息代码
     **/
    String ACTIG_ASST_FUNSUBCODE = "107";

    /**
     * 辅助信息-经济科目-信息代码
     **/
    String ACTIG_ASST_ECONSUBCODE = "108";

    //----------------------payTypeCode分类-----------------
    //差旅
    String PAY_TYPECODE_TRAVEL = "101";

    //培训
    String PAY_TYPECODE_TRAINING = "102";

    //其他费用
    String PAY_TYPECODE_OTHER = "000";

    //分摊
    String PAY_TYPECODE_SHARE = "103";

    //工资
    String PAY_TYPECODE_SALARY = "104";

    //合同
    String PAY_TYPECODE_CONTRACT = "105";

    //折旧
    String PAY_TYPECODE_DEPR = "106";

    //零星采购
    String PAY_TYPECODE_PURC = "107";

    //药品
    String PAY_TYPECODE_DRUG = "109";

    //物资采购
    String PAY_TYPECODE_PURC_WZ = "110";


    //-----------------------付款回单信息start------------------------

    //-----------------------sup_type 报销类型大类 start------------------------
    /*报销类型大类 1 费用报销*/
    String SUP_TYPE_FEE = "1";
    /*报销类型大类 2 药品报销*/
    String SUP_TYPE_DURG = "2";
    /*报销类型大类 3 工资报销*/
    String SUP_TYPE_SALARY = "3";
    /*报销类型大类 4 折旧报销*/
    String SUP_TYPE_DEPRECI = "4";

//-----------------------sup_type 报销类型大类 end------------------------


    //---------------status---------------
    /**
     * 识别成功
     **/
    String RECOG_SUCCESS = "1";

    /**
     * 识别失败
     */
    String RECOG_FAILD = "2";

    /**
     * 手动修改
     */
    String MANUAL_MODIFY = "3";
    //识别信息后，可能作为map中key值的日期名称
    String RECOG_DATE = "日期";
    String RECOG_COMMISSION_DATE = "委托日期";
    //识别信息后，可能作为map中key值的金额名称
    String RECOG_AMT = "金额";
    //-----------------------付款回单信息end------------------------


    //-----------------------付款回单类型start------------------------
    String BUSINESS_AUTHORIZATION_LETTER = "业务委托书";

    String BUSINESS_SETTLEMENT_LETTER = "结算业务申请书";

    String NATION_PAYMENT_VOUCHER = "国库集中支付凭证";

    String NATION_PAYMETN_RECEIPT = "国内支付业务付款回单";
    //-----------------------付款回单类型end----------------------------

    //-----------------------工资凭证类型start--------------------------

    /**
     * 工资计提
     **/
    String SALARY = "1";

    /**
     * 工资发放+个人代扣
     **/
    String INDIVDUAL_REDUCE = "2";

    /**
     * 企业缴纳
     **/
    String BUSINESS_PAYMENT = "3";

    /**
     * 工会经费
     **/
    String UNION_FUNDS = "4";
    //-----------------------工资凭证类型end--------------------------

    //-----------------------科室类型start--------------------------
    /**
     * 职能
     **/
    String FUN_DEPT = "1";

    /**
     * 窗口
     **/
    String WIN_DEPT = "2";

    /**
     * 临床
     **/
    String CLINI_DEPT = "3";

    /**
     * 医技
     **/
    String MED_TEC_DEPT = "4";

    /**
     * 临床医技
     **/
    String CLINIC_STR = "3,4";

    /**
     * 医辅、行政  （暂时都归到职能类型）
     **/
    String FUN_STR = "1";
    //-----------------------科室类型end--------------------------


    //-----------------------工资凭证人员类型start--------------------------
    //在编
    String[] ESTAB_STR_ARR = new String[]{"在编", "血防占编"};

    //招聘
    String[] HIRE_STR_ARR = new String[]{"编外-医技", "编外-护理", "编外-辅助岗位", "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习"};

    //临聘
    String[] TEMP_HIRE_STR_ARR = new String[]{"编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习"};

    //借调
    String[] SECONDMENT_STR_ARR = new String[]{"借调"};

    //返聘
    String[] REHIRE_STR_ARR = new String[]{"返聘"};

    //就业见习人员
    String EMP_TYPE_EMP_INSHIP = "696";

    //就业见习人员
    String EMP_TYPE_EMP_INSHIP_NAME = "就业见习";
    //-----------------------工资凭证人员类型end----------------------------

    //培训费目名称
    String TRAIN_ITEM_NAME = "培训费";

    //------------------------零星采购-经济科目 start----------------------------
    //总务低值易耗品
    String ZWDZYH = "3021801";
    //其他总务材料
    String QTZWCL = "3021802";
    //------------------------零星采购-经济科目 end------------------------------

    //------------------------药品库房类型-start---------------------------------
    //中药库
    String STOIN_TYPE_ZY = "1099";
    //西药库
    String STOIN_TYPE_XY = "1094";
    //消毒用品
    String STOIN_TYPE_XD = "1241";
    //------------------------药品库房类型-end-----------------------------------


    /**
     * 附件类型-普通
     **/
    String FILE_TYPE_GENERAL = "1";

    /**
     * 附件类型-付款证明
     **/
    String FILE_TYPE_PAY = "2";

    /**
     * 附件类型-发票
     **/
    String FILE_TYPE_INVO = "3";

    /**
     * 附件类型-启用
     **/
    String RULE_STATUS_ENABLE = "1";

    /**
     * 附件类型-禁用
     **/
    String RULE_STATUS_DISABLE = "0";


    //------------------------凭证模版-险种类型-start---------------------------------
    // 养老保险
    String INSURANCE_TYPE_ELDERLY_CARE = "30101";
    // 医疗保险
    String INSURANCE_TYPE_MED = "30102";
    // 失业保险
    String INSURANCE_TYPE_UN_EMP = "30103";
    // 生育保险
    String INSURANCE_TYPE_PROCREATE = "30104";
    // 工伤保险
    String INSURANCE_TYPE_INJURY = "30105";
    // 职业年金
    String INSURANCE_TYPE_ANNUITY = "30106";
    // 住房公积金
    String INSURANCE_TYPE_HOUSING_FUND = "30107";
    // 工会会费
    String INSURANCE_TYPE_UNION = "30108";
    // 人力临时扣款
    String MENPOWER_TEMP_DEDUCT = "910";
    // 财务临时扣款
    String FINAN_TEMP_DEDUCT = "920";

    // 人力临时增加
    String MENPOWER_TEMP_ADD = "930";
    // 财务临时扣款
    String FINAN_TEMP_ADD = "940";

    //------------------------凭证模版-险种类型-end-----------------------------------

    //------------------------业务凭证类型-start---------------------------------
    // busiVoucherType 工资计提 accrue_wag
    String BUSIVOUCHERTYPE_ACCRUE_WAG = "accrue_wag";

    // busiVoucherType 计提五险两金 accrue_entp_insur_gold
    String BUSIVOUCHERTYPE_ACCRUE_ENTP_INSUR_GOLD = "accrue_entp_insur_gold";

    // busiVoucherType 应付职工薪酬 accrue_wag
    String BUSIVOUCHERTYPE_PAYROLL_PAYABLE = "payroll_payable";

    // busiVoucherType 冲账 accrue_wag
    String BUSIVOUCHERTYPE_STRIKE_BALANCE = "strike_balance";

    // busiVoucherType 后勤公司特殊往来账 accrue_wag
    String BUSIVOUCHERTYPE_INTER_COMP_SPEC_ACC = "inter_comp_spec_acc";

    // busiVoucherType 单采血浆站特殊往来账 accrue_wag
    String BUSIVOUCHERTYPE_BLO_STATION_SPEC_ACC = "blo_station_spec_acc";

    // busiVoucherType 个人计提个人所得税 accrue_wag
    String BUSIVOUCHERTYPE_PROVI_PSN_IIT = "provi_psn_iit";

    // busiVoucherType 个人计提三险两金 accrue_wag
    String BUSIVOUCHERTYPE_PROVI_PSN_SI = "provi_psn_si";

    // busiVoucherType 个人计提工会会费 accrue_wag
    String BUSIVOUCHERTYPE_PROVI_PSN_UNION = "provi_psn_union";

    // busiVoucherType 个人计提临时扣款 accrue_wag
    String BUSIVOUCHERTYPE_PROVI_PSN_TEMP_DEDUCT = "provi_psn_temp_deduct";

    // busiVoucherType 实际发放 accrue_wag
    String BUSIVOUCHERTYPE_SALARY_PAYROLL = "salary_payroll";

    // busiVoucherType 预算凭证 accrue_wag
    String BUSIVOUCHERTYPE_SALARY_BUDGET = "salary_budget";

    // busiVoucherType 吴军兵特殊往来账 accrue_wag
    String BUSIVOUCHERTYPE_SPECIAL_ACCOUNT3077 = "special_account_3077";

    // busiVoucherType 就业见习特殊往来账 accrue_wag
    String BUSIVOUCHERTYPE_SPECIAL_ACCOUNT_EMPINSHIP = "special_account_emp_inship";


    //----------------费用报销类对应的业务凭证类型-end------------------------------
    //busiVoucherType 药品报销 drug_reim
    String BUSIVOUCHERTYPE_DRUG_REIM = "drug_reim";

    //busiVoucherType 采购报销 purc_reim
    String BUSIVOUCHERTYPE_PURC_REIM = "purc_reim";

    //busiVoucherType 其它报销 oth_reim
    String BUSIVOUCHERTYPE_OTH_REIM = "oth_reim";

    //busiVoucherType 合同报销 cntr_reim
    String BUSIVOUCHERTYPE_CNTR_REIM = "cntr_reim";

    //busiVoucherType 折旧计提 depr_accrue
    String BUSIVOUCHERTYPE_DEPR_ACCRUE = "depr_accrue";

    //busiVoucherType 分摊报销 aprt_reim
    String BUSIVOUCHERTYPE_APRT_REIM = "aprt_reim";

    //busiVoucherType 培训报销 train_reim
    String BUSIVOUCHERTYPE_TRAIN_REIM = "train_reim";


    //busiVoucherType 差旅费用报销 travel_reim
    String BUSIVOUCHERTYPE_TRAVEL_REIM = "travel_reim";

    //busiVoucherType 差旅费用报销_借 travel_reim_lend
    String BUSIVOUCHERTYPE_TRAVEL_REIM_LEND = "travel_reim_lend";

    //busiVoucherType 差旅费用报销_付款证明文件贷 travel_reim
    String BUSIVOUCHERTYPE_TRAVEL_REIM_NORMAL = "travel_reim_normal";

    //busiVoucherType 差旅费用报销_现金贷 travel_reim
    String BUSIVOUCHERTYPE_TRAVEL_REIM_CASH = "travel_reim_cash";

    //busiVoucherType 往来支付报销 cmgo_pay
    String BUSIVOUCHERTYPE_CMGO_PAY = "cmgo_pay";

    //busiVoucherType 电费报销 elect_bill
    String BUSIVOUCHERTYPE_ELECT_BILL = "elect_bill";

    //busiVoucherType 水费报销 water_bill
    String BUSIVOUCHERTYPE_WATER_BILL = "water_bill";

    //busiVoucherType 燃气费报销 gas_bill
    String BUSIVOUCHERTYPE_GAS_BILL = "gas_bill";

    //busiVoucherType 电话费报销 tel_bill
    String BUSIVOUCHERTYPE_TEL_BILL = "tel_bill";

    //busiVoucherType 西药报销 wm
    String BUSIVOUCHERTYPE_WM = "wm";

    //busiVoucherType 中草药报销 tcmherb
    String BUSIVOUCHERTYPE_TCMHERB = "tcmherb";

    //busiVoucherType 消毒用品报销 disinfect_supp
    String BUSIVOUCHERTYPE_DISINFECT_SUPP = "disinfect_supp";

    //------------------------业务凭证类型-end-----------------------------------


    //------------------------业务凭证借贷-start---------------------------------
    // 财务会计
    String ACTIGSYS_TYPE_1 = "1";
    // 预算会计
    String ACTIGSYS_TYPE_2 = "2";
    // 凭证借方
    String AMOUNT_TYPE_1 = "1";
    // 凭证贷方
    String AMOUNT_TYPE_2 = "2";
    //------------------------业务凭证借贷-end-----------------------------------


    //------------------------业务凭证类型对应的业务组件Map-start------------------
    /**
     * 构建业务凭证类型对应的业务组件Map
     */
    Map<String, String> busiVouchervCoverNodeMap = new HashMap<String, String>() {{
        //计提工资-组件前缀
        put("accrue_wag", "SalaryVcrAccrueWagNode");
        //计提五险两金-组件前缀
        put("accrue_entp_insur_gold", "SalaryVcrAccrueEntpInsurGoldNode");
        // 应付职工薪酬-组件前缀
        put("payroll_payable", "SalaryVcrPayrollPayableNode");
        // 冲账-组件前缀
        put("strike_balance", "SalaryVcrStrikeBalanceNode");
        // 后勤公司特殊往来账-组件前缀
        put("inter_comp_spec_acc", "SalaryVcrInterCompSpecAccNode");
        // 单采血浆站特殊往来账-组件前缀
        put("blo_station_spec_acc", "SalaryVcrBloStationSpecAccNode");
        // 个人计提个人所得税-组件前缀
        put("provi_psn_iit", "SalaryVcrProviPsnIitNode");
        // 个人计提三险两金-组件前缀
        put("provi_psn_si", "SalaryVcrProviPsnSiNode");
        // 个人计提工会会费-组件前缀
        put("provi_psn_union", "SalaryVcrProviPsnUnionNode");
        // 个人计提临时扣款-组件前缀
        put("provi_psn_temp_deduct", "SalaryVcrProviPsnTempDeductNode");
        // 实际发放-组件前缀
        put("salary_payroll", "SalaryVcrSalaryPayrollNode");
        // 预算凭证-组件前缀
        put("salary_budget", "SalaryVcrSalaryBudgetNode");
    }};
    //------------------------业务凭证类型对应的业务组件Map-end-----------------------

    //------------------------凭证类型对应的流程链前缀Map-start-----------------------
    /**
     * 构建业务凭证类型对应的流程链前缀Map
     */
    Map<String, String> busiVouchervCoverChainMap = new HashMap<>() {{
        //计提工资-组件前缀
        put("accrue_wag", "SalaryVcrAccrueWagChain");
        //计提五险两金-组件前缀
        put("accrue_entp_insur_gold", "SalaryVcrAccrueEntpInsurGoldChain");
        //工资发放+个人代扣
        put("wag_issu_psn_with_hold", "SalaryVcrwagIssuPsnWithHoldChain");

    }};

    //------------------------凭证类型对应的流程链前缀Map-end-----------------------

}
