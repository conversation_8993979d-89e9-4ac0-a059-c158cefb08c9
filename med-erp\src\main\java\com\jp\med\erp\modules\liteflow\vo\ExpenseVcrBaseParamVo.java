package com.jp.med.erp.modules.liteflow.vo;

import com.jp.med.common.vo.EmpEmployeeDictVo;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryRelCoConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 费用凭证业务基础参数配置
 */
@Data
public class ExpenseVcrBaseParamVo {

    /**
     * 报销明细数据
     */
    List<ErpReimDetailVo> erpReimDetailVos = new ArrayList<>();

    /**
     * 报销ID列表
     */
    private List<Integer> ids;

    /**
     * 科室映射对照
     */
    Map<String, HrmOrgAgencyMapVo> orgMappings;

    /**
     * 业务场景对应的凭证模版map
     */
    Map<String, List<VoucherTemplateDto>> voucheTmeplateDtoMap;

    /**
     * 凭证业务分类
     * 1：报销
     * 2：药品
     * 3：工资
     * 4：折旧
     */
    private String supType;

    /**
     * 凭证业务报销分类 费用报销: 1-差旅 2-培训 3-其他费用 4-分摊费用 6-合同 8-采购 10-物资采购 11-其他费用(无发票) 12-往来支付 13-借款
     */
    private String reimType;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 医疗机构编码
     */
    String hospitalId;

    /**
     * 业务凭证类型
     */
    String busiVoucherType;

    /**
     * 凭证分组编码
     */
    String ruleGroupId;

    /** 付款方式 0 非现金  1：现金  可拓展 **/
    private String payMethod;


    /**
     * 获取报销费用名称
     *
     * @return
     */
    public String getReimNameType() {
        switch (reimType) {
            case "1":
                return "travel_reim";           // 差旅报销
            case "2":
                return "training_reim";         // 培训报销
            case "3":
                return "other_fee_reim";        // 其他费用
            case "4":
                return "allocation_fee_reim";   // 分摊费用
            case "6":
                return "contract_reim";         // 合同
            case "8":
                return "purchase_reim";         // 采购凭证
            case "10":
                return "material_purchase";    // 物资采购
            case "11":
                return "other_fee_no_invoice"; // 其他费用(无发票)
            case "12":
                return "transaction_payment";  // 往来支付
            case "13":
                return "loan_reim";           // 借款
            default:
                throw new RuntimeException("不支持的费用凭证类型: " + reimType);
        }
    }

}
