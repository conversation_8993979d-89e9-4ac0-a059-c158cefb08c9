package com.jp.med.erp.modules.liteflow.util;

import com.alibaba.fastjson.JSON;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.ValidateUtil;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryRelCoConfigVo;
import com.jp.med.erp.modules.liteflow.vo.*;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherEntryDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 凭证模版匹配引擎
 * 根据业务数据匹配最合适的凭证模版
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class VoucherTemplateEngine {

    /**
     * 匹配最佳凭证模版
     *
     * @param templates     可用模版列表
     * @param vcrFlowBaseVo 工资凭证上下文
     * @return 匹配的模版，如果没有匹配则返回Optional.empty()
     */
    public Optional<VoucherTemplateDto> matchBestTemplate(List<VoucherTemplateDto> templates, VcrFlowDetailBaseVo vcrFlowBaseVo) {
        return matchBestTemplate(templates, vcrFlowBaseVo, null, null, null);
    }

    /**
     * 匹配最佳凭证模版（支持会计科目匹配）
     *
     * @param templates         可用模版列表
     * @param vcrFlowBaseVo     工资凭证上下文
     * @param accountingType    会计类型：1(财务会计) / 2(预算会计)
     * @param debitOrCredit     借贷方向：1(借方) / 2(贷方)
     * @param targetSubjectCode 目标科目编码
     * @return 匹配的模版，如果没有匹配则返回Optional.empty()
     */
    public Optional<VoucherTemplateDto> matchBestTemplate(List<VoucherTemplateDto> templates,
                                                          VcrFlowDetailBaseVo vcrFlowBaseVo,
                                                          String accountingType,
                                                          String debitOrCredit,
                                                          String targetSubjectCode) {
        log.info("开始匹配凭证模版，候选模版数量: {}, 匹配条件: {}, 会计类型: {}, 借贷方向: {}, 目标科目: {}",
                templates.size(), vcrFlowBaseVo, accountingType, debitOrCredit, targetSubjectCode);

        // 按优先级排序，优先级越高越优先匹配
        templates.sort((t1, t2) -> Integer.compare(t2.getPriority(), t1.getPriority()));

        // 遍历模版，找到第一个匹配的
        for (VoucherTemplateDto template : templates) {
            if (isTemplateMatched(template, vcrFlowBaseVo, accountingType, debitOrCredit, targetSubjectCode)) {
                log.info("找到匹配的模版: {}", template.getRuleName());
                return Optional.of(template);
            }
        }

        log.warn("未找到匹配的凭证模版，上下文: {}", vcrFlowBaseVo);
        return Optional.empty();
    }

    /**
     * 检查模版是否匹配给定的上下文
     *
     * @param template 凭证模版
     * @param context  工资凭证上下文
     * @return 是否匹配
     */
    private boolean isTemplateMatched(VoucherTemplateDto template, VcrFlowDetailBaseVo context) {
        return isTemplateMatched(template, context, null, null, null);
    }

    /**
     * 检查模版是否匹配给定的上下文（支持会计科目匹配）
     *
     * @param template          凭证模版
     * @param context           工资凭证上下文
     * @param accountingType    会计类型
     * @param debitOrCredit     借贷方向
     * @param targetSubjectCode 目标科目编码
     * @return 是否匹配
     */
    private boolean isTemplateMatched(VoucherTemplateDto template,
                                      VcrFlowDetailBaseVo context,
                                      String accountingType,
                                      String debitOrCredit,
                                      String targetSubjectCode) {
        log.debug("检查模版匹配: {} vs {}", template.getRuleName(), context);

        // 检查科室类型
        if (!matchDeptType(template.getDeptType(), context.getDeptType())) {
            log.debug("科室类型不匹配: 模版要求={}, 实际={}", template.getDeptType(), context.getDeptType());
            return false;
        }

        // 检查人员类型
        if (!matchPersonnelType(template.getPersonnelType(), context.getEmpType())) {
            log.debug("人员类型不匹配: 模版要求={}, 实际={}", template.getPersonnelType(), context.getEmpType());
            return false;
        }

        // 检查经济科目
        if (!matchEconomicSubject(template.getEconomicSubject(), context.getEconSubCode())) {
            log.debug("经济科目不匹配: 模版要求={}, 实际={}", template.getEconomicSubject(), context.getEconSubCode());
            return false;
        }

        // 检查资金性质
        if (!matchFundNature(template.getFundNature(), context.getFundNature())) {
            log.debug("资金性质不匹配: 模版要求={}, 实际={}", template.getFundNature(), context.getFundNature());
            return false;
        }

        // 检查险种类型
        if (!matchInsuranceType(template.getInsuranceType(), context.getInsuranceType())) {
            log.debug("险种类型不匹配: 模版要求={}, 实际={}", template.getInsuranceType(), context.getInsuranceType());
            return false;
        }

        // 检查科研归属
        if (!matchResearchAttribution(template.getResearchAttribution(), context.getResearchAttribution())) {
            log.debug("科研归属不匹配: 模版要求={}, 实际={}", template.getResearchAttribution(), context.getResearchAttribution());
            return false;
        }

        // 检查启用年度
        if (!matchEnableYear(template.getEnableYear(), context.getEnableYear())) {
            log.debug("启用年度不匹配: 模版要求={}, 实际={}", template.getEnableYear(), context.getEnableYear());
            return false;
        }

        // 检查会计科目匹配（如果指定了相关参数）
        if (StringUtils.isNotEmpty(accountingType) && StringUtils.isNotEmpty(debitOrCredit) && StringUtils.isNotEmpty(targetSubjectCode)) {
            if (!matchAccountingSubject(template, accountingType, debitOrCredit, targetSubjectCode)) {
                log.debug("会计科目不匹配: 模版={}, 会计类型={}, 借贷方向={}, 目标科目={}",
                        template.getRuleName(), accountingType, debitOrCredit, targetSubjectCode);
                return false;
            }
        }

        log.debug("模版匹配成功: {}", template.getRuleName());
        return true;
    }

    /**
     * 匹配科室类型
     */
    private boolean matchDeptType(String templateDeptType, String contextDeptType) {
        if (StringUtils.isEmpty(templateDeptType)) {
            return true; // 模版没有限制科室类型
        }
        return StringUtils.equals(templateDeptType, contextDeptType);
    }

    /**
     * 匹配人员类型
     * 支持多选，JSON数组格式
     */
    private boolean matchPersonnelType(String templatePersonnelType, String contextEmpType) {
        if (StringUtils.isEmpty(templatePersonnelType)) {
            return true; // 模版没有限制人员类型
        }

        try {
            // 尝试解析为JSON数组
            List<String> allowedTypes = JSON.parseArray(templatePersonnelType, String.class);
            return allowedTypes.contains(contextEmpType);
        } catch (Exception e) {
            // 如果不是JSON数组，则作为单个值处理
            return StringUtils.equals(templatePersonnelType, contextEmpType);
        }
    }

    /**
     * 匹配经济科目
     * 支持根据工资项目名称匹配经济科目
     */
    private boolean matchEconomicSubject(String templateEconomicSubject, String contextReimCode) {
        if (StringUtils.isEmpty(templateEconomicSubject)) {
            return true; // 模版没有限制经济科目
        }
        // 根据工资项目名称映射到经济科目
        return StringUtils.equals(templateEconomicSubject, contextReimCode);
    }

    /**
     * 匹配资金性质
     */
    private boolean matchFundNature(String templateFundNature, String contextFundNature) {
        if (StringUtils.isEmpty(templateFundNature)) {
            return true; // 模版没有限制资金性质
        }
        return StringUtils.equals(templateFundNature, contextFundNature);
    }

    /**
     * 匹配险种类型
     */
    private boolean matchInsuranceType(String templateInsuranceType, String contextInsuranceType) {
        if (StringUtils.isEmpty(templateInsuranceType)) {
            return true; // 模版没有限制险种类型
        }
        return StringUtils.equals(templateInsuranceType, contextInsuranceType);
    }

    /**
     * 匹配科研归属
     */
    private boolean matchResearchAttribution(String templateResearchAttribution, String contextResearchAttribution) {
        if (StringUtils.isEmpty(templateResearchAttribution)) {
            return true; // 模版没有限制科研归属
        }
        return StringUtils.equals(templateResearchAttribution, contextResearchAttribution);
    }

    /**
     * 匹配启用年度
     */
    private boolean matchEnableYear(String templateEnableYear, String contextEnableYear) {
        if (StringUtils.isEmpty(templateEnableYear)) {
            return true; // 模版没有限制启用年度
        }
        return StringUtils.equals(templateEnableYear, contextEnableYear);
    }

    /**
     * 匹配会计科目
     *
     * @param template          凭证模版
     * @param accountingType    会计类型
     * @param debitOrCredit     借贷方向
     * @param targetSubjectCode 目标科目编码
     * @return 是否匹配
     */
    private boolean matchAccountingSubject(VoucherTemplateDto template,
                                           String accountingType,
                                           String debitOrCredit,
                                           String targetSubjectCode) {
        if (StringUtils.isEmpty(targetSubjectCode)) {
            return true; // 如果没有指定目标科目，则不进行科目匹配
        }

        // 从模版中提取指定位置的科目编码
        String templateSubjectCode = extractSubjectCodeFromTemplate(template, accountingType, debitOrCredit);

        if (StringUtils.isEmpty(templateSubjectCode)) {
            log.debug("模版中未找到指定位置的科目编码: accountingType={}, debitOrCredit={}", accountingType, debitOrCredit);
            return false;
        }

        boolean matched = StringUtils.equals(templateSubjectCode, targetSubjectCode);
        log.debug("科目匹配结果: 模版科目={}, 目标科目={}, 匹配结果={}", templateSubjectCode, targetSubjectCode, matched);

        return matched;
    }

    /**
     * 从模版中提取指定位置的科目编码
     *
     * @param template       凭证模版
     * @param accountingType 会计类型：FINANCIAL(财务会计) / BUDGET(预算会计)
     * @param debitOrCredit  借贷方向：DEBIT(借方) / CREDIT(贷方)
     * @return 科目编码，如果未找到返回null
     */
    private String extractSubjectCodeFromTemplate(VoucherTemplateDto template,
                                                  String accountingType,
                                                  String debitOrCredit) {
        try {
            // 根据会计类型获取对应的分录配置
            String entryPairJson = null;
            if (ErpConstants.ACTIGSYS_TYPE_1.equals(accountingType)) {
                entryPairJson = template.getFinancialEntryPair();
            } else if (ErpConstants.ACTIGSYS_TYPE_2.equals(accountingType)) {
                entryPairJson = template.getBudgetEntryPair();
            }

            if (StringUtils.isEmpty(entryPairJson)) {
                log.debug("模版中未配置{}分录: {}", accountingType, template.getRuleName());
                return null;
            }

            // 解析分录配置
            VcrEntryPairVo entryPair = JSON.parseObject(entryPairJson, VcrEntryPairVo.class);
            if (entryPair == null) {
                log.debug("解析分录配置失败: {}", entryPairJson);
                return null;
            }

            // 根据借贷方向获取对应的分录
            VoucherEntryDto entry = null;
            if (ErpConstants.AMOUNT_TYPE_1.equals(debitOrCredit)) {
                entry = entryPair.getDebitEntry();
            } else if (ErpConstants.AMOUNT_TYPE_2.equals(debitOrCredit)) {
                entry = entryPair.getCreditEntry();
            }

            if (entry == null) {
                log.debug("未找到{}分录", debitOrCredit);
                return null;
            }

            String subjectCode = entry.getSubjectCode();
            log.debug("提取到科目编码: 会计类型={}, 借贷方向={}, 科目编码={}", accountingType, debitOrCredit, subjectCode);

            return subjectCode;

        } catch (Exception e) {
            log.error("提取科目编码时发生异常: template={}, accountingType={}, debitOrCredit={}",
                    template.getRuleName(), accountingType, debitOrCredit, e);
            return null;
        }
    }

    /**
     * 应用模版生成辅助项（费用报销重写）
     */
    public List<ErpReimAsstDto> applyTemplateToGenerateAssts(VoucherTemplateDto template,
                                                             VcrFlowDetailBaseVo vcrFlowDetailBaseVo,
                                                             VcrFlowContextBaseVo flowContext) {
        List<ErpReimAsstDto> assts = new ArrayList<>();
        // 处理财务会计分录
        if (StringUtils.isNotEmpty(template.getFinancialEntryPair())) {
            try {
                VcrEntryPairVo financialPair =
                        JSON.parseObject(template.getFinancialEntryPair(), VcrEntryPairVo.class);
                assts.addAll(generateAsstsFromEntryPair(financialPair, vcrFlowDetailBaseVo, flowContext, ErpConstants.ACTIGSYS_TYPE_1));
            } catch (Exception e) {
                log.error("解析财务会计分录配置失败: {}", template.getFinancialEntryPair(), e);
            }
        }

        // 处理预算会计分录
        if (StringUtils.isNotEmpty(template.getBudgetEntryPair())) {
            try {
                VcrEntryPairVo budgetPair =
                        JSON.parseObject(template.getBudgetEntryPair(), VcrEntryPairVo.class);
                assts.addAll(generateAsstsFromEntryPair(budgetPair, vcrFlowDetailBaseVo, flowContext, ErpConstants.ACTIGSYS_TYPE_2));
            } catch (Exception e) {
                log.error("解析预算会计分录配置失败: {}", template.getBudgetEntryPair(), e);
            }
        }

        return assts;
    }

    /**
     * 从分录对生成辅助项
     */
    private List<ErpReimAsstDto> generateAsstsFromEntryPair(VcrEntryPairVo entryPair,
                                                            VcrFlowDetailBaseVo vcrFlowDetailBaseVo,
                                                            VcrFlowContextBaseVo flowContext,
                                                            String actigSys) {
        List<ErpReimAsstDto> assts = new ArrayList<>();

        // 生成借方辅助项 ,借方配置会计科目不为空才进行生成
        if (entryPair.getDebitEntry() != null && !ValidateUtil.isEmpty(entryPair.getDebitEntry().getSubjectCode())) {
            ErpReimAsstDto debitAsst = createAsstFromEntry(
                    entryPair.getDebitEntry(), vcrFlowDetailBaseVo, flowContext, actigSys, ErpConstants.AMOUNT_TYPE_1);
            assts.add(debitAsst);
        }

        // 生成贷方辅助项,贷方配置会计科目不为空才进行生成
        if (entryPair.getCreditEntry() != null && !ValidateUtil.isEmpty(entryPair.getCreditEntry().getSubjectCode())) {
            ErpReimAsstDto creditAsst = createAsstFromEntry(
                    entryPair.getCreditEntry(), vcrFlowDetailBaseVo, flowContext, actigSys, ErpConstants.AMOUNT_TYPE_2);
            assts.add(creditAsst);
        }

        return assts;
    }

    /**
     * 从分录配置创建辅助项
     */
    private ErpReimAsstDto createAsstFromEntry(VoucherEntryDto entry,
                                               VcrFlowDetailBaseVo vcrFlowDetailBaseVo,
                                               VcrFlowContextBaseVo flowContext,
                                               String actigSys,
                                               String amountType) {
        ErpReimAsstDto asst = new ErpReimAsstDto();
        // 基础信息
        asst.setSupType(flowContext.getSupType()); // 工资凭证
        asst.setReimDetailId(flowContext.getTaskId());
        asst.setAsstNo(flowContext.getNextAsstNo());
        asst.setActigSys(actigSys);
        asst.setActigAmtType(amountType);
        asst.setActigSubCode(entry.getSubjectCode());
        asst.setActigSubName(entry.getSubjectName());
        asst.setActigAmt(vcrFlowDetailBaseVo.getReimAmt());
        String empTypeDesc = "";
        if (StringUtils.isNotEmpty(vcrFlowDetailBaseVo.getEmpTypeDesc())) {
            empTypeDesc = "[" + vcrFlowDetailBaseVo.getEmpTypeDesc() + "]";
        }
        asst.setAbst(vcrFlowDetailBaseVo.getReimDesc() + empTypeDesc);

        // 处理辅助项信息
        if (entry.getAuxiliary() != null && !entry.getAuxiliary().isEmpty()) {
            processAuxiliaryFields(asst, entry.getAuxiliary(), vcrFlowDetailBaseVo, flowContext);
        }

        // 设置创建信息
        asst.setCrter(flowContext.getCrter());
        asst.setCreateTime(DateUtil.getCurrentTime(null));
        asst.setHospitalId(flowContext.getHospitalId());

        return asst;
    }

    /**
     * 处理辅助项字段
     */
    private void processAuxiliaryFields(ErpReimAsstDto asst, Map<String, Object> auxiliary,
                                        VcrFlowDetailBaseVo vcrFlowDetailBaseVo, VcrFlowContextBaseVo flowContext
    ) {

        // 1、处理现金流量辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_CASHFLOWCODE)) {
            asst.setCashFlowCode((String) auxiliary.get(ErpConstants.ACTIG_ASST_CASHFLOWCODE));
            asst.setCashFlowName((String) auxiliary.get(ErpConstants.ACTIG_ASST_CASHFLOWCODE + "Name"));
        }

        // 2、处理往来单位辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_RELCOCODE)) {
            String relCoValue = (String) auxiliary.get(ErpConstants.ACTIG_ASST_RELCOCODE);
            if ("SystemAuto".equals(relCoValue)) {
                // 自动填充逻辑，从往来单位里获取对应的往来单位信息
                List<ErpVcrSalaryRelCoConfigVo> empRelCoList = flowContext.getErpVcrSalaryRelCoConfigMap().get(vcrFlowDetailBaseVo.getEmpCode());
                if (!ValidateUtil.isEmpty(empRelCoList)) {
                    asst.setRelCoCode(empRelCoList.get(0).getRelCoCode());
                    asst.setRelCoName(empRelCoList.get(0).getRelCoName());
                } else {
                    //无配置则暂时不做处理
                }
            } else {
                asst.setRelCoCode(relCoValue);
                asst.setRelCoName((String) auxiliary.get(ErpConstants.ACTIG_ASST_RELCOCODE + "Name"));
            }
        }

        // 3、处理资金性质辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_FUNDTYPE)) {
            asst.setFundType((String) auxiliary.get(ErpConstants.ACTIG_ASST_FUNDTYPE));
            asst.setFundTypeName((String) auxiliary.get(ErpConstants.ACTIG_ASST_FUNDTYPE + "Name"));
        }

        // 4、处理项目辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_PROJCODE)) {
            asst.setProjCode((String) auxiliary.get(ErpConstants.ACTIG_ASST_PROJCODE));
            asst.setProjName((String) auxiliary.get(ErpConstants.ACTIG_ASST_PROJCODE + "Name"));
        }

        // 5、处理部门辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_CFG_DEPT)) {
            String deptValue = (String) auxiliary.get(ErpConstants.ACTIG_ASST_CFG_DEPT);
            if ("SystemAuto".equals(deptValue)) {
                asst.setDeptCode(vcrFlowDetailBaseVo.getOrgId());
                asst.setDeptName(vcrFlowDetailBaseVo.getOrgName());
            } else {
                asst.setDeptCode(deptValue);
                asst.setDeptName((String) auxiliary.get(ErpConstants.ACTIG_ASST_CFG_DEPT + "Name"));
            }
        }

        // 6、处理功能科目辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_FUNSUBCODE)) {
            asst.setFunSubCode((String) auxiliary.get(ErpConstants.ACTIG_ASST_FUNSUBCODE));
            asst.setFunSubName((String) auxiliary.get(ErpConstants.ACTIG_ASST_FUNSUBCODE + "Name"));
        }

        // 7、处理功能科目辅助项
        if (auxiliary.containsKey(ErpConstants.ACTIG_ASST_ECONSUBCODE)) {
            asst.setEconSubCode((String) auxiliary.get(ErpConstants.ACTIG_ASST_ECONSUBCODE));
            asst.setEconSubName((String) auxiliary.get(ErpConstants.ACTIG_ASST_ECONSUBCODE + "Name"));
        }
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    public ExpenseVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description, String reimType) {
        ExpenseVcrTaskDetialVo detail = new ExpenseVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setReimType(reimType);
        return detail;
    }

}