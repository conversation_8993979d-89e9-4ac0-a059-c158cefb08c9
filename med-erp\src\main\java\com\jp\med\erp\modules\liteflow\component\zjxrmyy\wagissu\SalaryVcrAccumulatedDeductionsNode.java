package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.util.ValidateUtil;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 累计扣款凭证生成节点
 * 处理临时扣款和其他扣款项目的凭证生成
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrAccumulatedDeductionsNode", name = "累计扣款凭证生成")
public class SalaryVcrAccumulatedDeductionsNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Override
    public void process() throws Exception {
        log.info("开始执行累计扣款凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        // 获取个人代扣任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
                .stream()
                .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.INDIVDUAL_REDUCE))
                .collect(Collectors.toList());

        if (taskDetails.isEmpty()) {
            log.info("没有个人代扣任务明细，跳过累计扣款处理");
            return;
        }

        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理累计扣款
            insertAssts.addAll(processAccumulatedDeductions(baseParamDtos, flowContext, taskDetails));

        } catch (Exception e) {
            log.error("累计扣款凭证生成失败", e);
            flowContext.markFlowFailed("累计扣款凭证生成失败: " + e.getMessage());
            throw e;
        }

        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);

        log.info("累计扣款凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理累计扣款
     */
    private List<ErpReimAsstDto> processAccumulatedDeductions(SalaryVcrBaseParamVo baseParamDtos,
                                                              SalaryVcrFlowContextVo flowContext,
                                                              List<SalaryVcrTaskDetialVo> taskDetails) {
        log.info("开始处理累计扣款");

        List<ErpReimAsstDto> result = new ArrayList<>();

        // 按项目类型分组处理扣款
        Map<String, List<SalaryVcrTaskDetialVo>> groupedByReimName = taskDetails.stream()
                .collect(Collectors.groupingBy(SalaryVcrTaskDetialVo::getReimName));

        for (Map.Entry<String, List<SalaryVcrTaskDetialVo>> entry : groupedByReimName.entrySet()) {
            String reimName = entry.getKey();
            List<SalaryVcrTaskDetialVo> details = entry.getValue();

            List<VoucherTemplateDto> templates = getTemplatesByReimName(baseParamDtos, reimName);

            if (templates.isEmpty()) {
                log.debug("未找到项目 {} 的凭证模版配置，跳过处理", reimName);
                continue;
            }

            if (StringUtils.equals(reimName, ErpConstants.TEMPORARY_REDUCE_SALARY)
                    || StringUtils.equals(reimName, ErpConstants.TEMPORARY_REDUCE_SALARY2)) {
                // 临时扣款每个用户单独处理
                for (SalaryVcrTaskDetialVo detail : details) {
                    if (detail.getReimAmt() != null) {
                        //计算每个用户的临时扣款
                        processIndividualDeduction(result, templates, detail, flowContext);
                    }
                }
            } else {
                // 其他项目汇总处理
                BigDecimal totalAmount = details.stream()
                        .map(SalaryVcrTaskDetialVo::getReimAmt)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    String insuranceType = details.get(0).getInsuranceType();
//                    SalaryVcrTemplateTaskDetialVo virtualDetail = createVirtualDetail(reimName, totalAmount, "累计扣款-" + reimName, insuranceType);
                    SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(reimName, totalAmount, null, insuranceType);
                    processIndividualDeduction(result, templates, virtualDetail, flowContext);
                }
            }
        }

        log.info("累计扣款处理完成，生成 {} 个辅助项", result.size());
        return result;
    }

    /**
     * 处理单个扣款项目
     */
    private void processIndividualDeduction(List<ErpReimAsstDto> result,
                                            List<VoucherTemplateDto> templates,
                                            SalaryVcrTaskDetialVo detail,
                                            SalaryVcrFlowContextVo flowContext) {
        Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, detail);
        if (matchedTemplate.isPresent()) {
            if (ValidateUtil.isEmpty(detail.getReimDesc())) {
                //凭证备注为空，获取模版中的凭证
                detail.setReimDesc(matchedTemplate.get().getRuleName());
            }
            List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                    matchedTemplate.get(), detail, flowContext);
            // 设置累计扣款的数据进入上下文
            flowContext.getAccumulatedDeductionsDetailList().add(detail);
            result.addAll(detailAssts);
            log.debug("扣款项目 {} 应用模版生成 {} 个辅助项", detail.getReimName(), detailAssts.size());
        } else {
            log.warn("未找到匹配的凭证模版，扣款项目: {}", detail.getReimName());
        }
    }

    /**
     * 根据项目名称获取模版
     */
    private List<VoucherTemplateDto> getTemplatesByReimName(SalaryVcrBaseParamVo baseParamDtos, String reimName) {
        // 映射项目名称到业务凭证类型
        String busiVoucherType = mapReimNameToBusiVoucherType(reimName);
        return baseParamDtos.getVoucheTmeplateDtoMap().getOrDefault(busiVoucherType, new ArrayList<>());
    }

    /**
     * 映射项目名称到业务凭证类型
     */
    private String mapReimNameToBusiVoucherType(String reimName) {
        Map<String, String> mapping = new HashMap<>();
        // 个人计提临时扣款
        mapping.put(ErpConstants.TEMPORARY_REDUCE_SALARY, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_TEMP_DEDUCT);
        mapping.put(ErpConstants.TEMPORARY_REDUCE_SALARY2, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_TEMP_DEDUCT);
        // 个人计提五险两金
        mapping.put(ErpConstants.PENSION_INSURANCE, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
        mapping.put(ErpConstants.MEDICAL_INSURANCE, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
        mapping.put(ErpConstants.UNEMPLOYMENT_INSURANCE, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
        mapping.put(ErpConstants.HOUSING_FUND, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
        mapping.put(ErpConstants.OCCUPATION_ANNUITY, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
        // 个人计提工会会费
        mapping.put(ErpConstants.LABOR_UNION, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_UNION);
        // 个人计提个人所得税
        mapping.put(ErpConstants.PERSON_TAX, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_IIT);
        // 可以添加更多的映射关系
        return mapping.getOrDefault(reimName, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_TEMP_DEDUCT);
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description, String insuranceType) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        detail.setInsuranceType(insuranceType);
        return detail;
    }
}