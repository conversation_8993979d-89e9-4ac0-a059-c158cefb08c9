package com.jp.med.erp.modules.liteflow.component.expense;

import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.enums.FeeNameEnum;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimDetailVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;

import javax.annotation.Resource;
import java.util.Optional;

import java.util.ArrayList;
import java.util.List;

/**
 * 费用报销凭证生成组件
 * 负责根据不同费用类型生成对应的凭证辅助项
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "ExpenseVoucherGenerateComponent", name = "费用报销凭证生成")
public class ExpenseVoucherGenerateComponent extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Override
    public void process() throws Exception {
        log.info("开始执行费用报销凭证生成节点");

        // 获取流程参数
        VoucherFlowParam param = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);

        try {
//            // 从上下文获取报销明细数据
//            @SuppressWarnings("unchecked")
//            List<ErpReimDetailVo> erpReimDetailVos = (List<ErpReimDetailVo>) flowContext.getExtParams().get("erpReimDetailVos");
//
//            if (erpReimDetailVos == null || erpReimDetailVos.isEmpty()) {
//                throw new RuntimeException("未找到报销明细数据");
//            }
//
//            // 1. 获取凭证模版列表（按业务凭证类型分组）
//            // 这里假设VoucherTemplateDto列表通过extParams传递，key为"voucherTemplates"，如无则抛异常
//            @SuppressWarnings("unchecked")
//            List<VoucherTemplateDto> templates = (List<VoucherTemplateDto>) flowContext.getExtParams().get("voucherTemplates");
//            if (templates == null || templates.isEmpty()) {
//                log.warn("未找到费用报销凭证模版配置");
//                throw new RuntimeException("未找到费用报销凭证模版配置");
//            }
//
//            log.info("找到费用报销凭证模版数量: {}", templates.size());
//
//            List<ErpReimAsstDto> insertAssts = new ArrayList<>();
//
//            insertAssts.addAll()
//
//            // 2. 遍历明细，基于模版引擎生成辅助项
//            for (ErpReimDetailVo detail : erpReimDetailVos) {
//                // 跳过金额为0的明细
//                if (detail.getSum() == null || detail.getSum().compareTo(java.math.BigDecimal.ZERO) == 0) {
//                    log.debug("跳过金额为0的明细: {}", detail.getReimName());
//                    continue;
//                }
//                // 匹配最佳模版
//                Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, detail);
//                if (matchedTemplate.isPresent()) {
//                    // 应用模版生成辅助项
//                    List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
//                            matchedTemplate.get(), detail, flowContext);
//                    insertAssts.addAll(detailAssts);
//                    log.debug("应用模版 {} 生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
//                } else {
//                    log.warn("未找到匹配的凭证模版，上下文: {}", detail);
//                    throw new RuntimeException("未找到匹配的凭证模版: " + detail.getReimName());
//                }
//            }
//
//            // 设置创建人员信息
//            insertAssts.forEach(asst -> {
//                asst.setCrter(param.getRecordPersonId());
//                asst.setCreateTime(DateUtil.getCurrentTime(null));
//                asst.setHospitalId(param.getHospitalId());
//            });
//
//            // 添加到上下文
//            flowContext.addGeneratedAssts(insertAssts);
//            log.info("费用报销凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());

        } catch (Exception e) {
            log.error("费用报销凭证生成失败", e);
            flowContext.markFlowFailed("费用报销凭证生成失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 生成差旅报销凭证
     */
    private List<ErpReimAsstDto> generateTravelVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                            List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成差旅报销凭证");
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 调用原有的差旅报销逻辑
        // 这里可以重用原有的 handleTravelAndOldTrainingAssts 方法的核心逻辑

        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成培训报销凭证
     */
    private List<ErpReimAsstDto> generateTrainingVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                              List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成培训报销凭证");
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 调用原有的培训报销逻辑
        // 这里可以重用原有的 generalTrainingAssts 方法的核心逻辑

        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成其他费用凭证
     */
    private List<ErpReimAsstDto> generateOtherFeeVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                              List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成其他费用凭证");
        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成分摊费用凭证
     */
    private List<ErpReimAsstDto> generateAllocationFeeVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                                   List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成分摊费用凭证");
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 调用原有的分摊费用逻辑
        // 这里可以重用原有的 generalFeeAllocationAsst 方法的核心逻辑

        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成合同凭证
     */
    private List<ErpReimAsstDto> generateContractVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                              List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成合同凭证");
        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成采购凭证
     */
    private List<ErpReimAsstDto> generatePurchaseVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                              List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成采购凭证");
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 调用原有的采购凭证逻辑
        // 这里可以重用原有的 generalPurcAsst 方法的核心逻辑
        // 采购凭证比较复杂，需要单独处理付款方式等逻辑

        return insertAssts;
    }

    /**
     * 生成无发票费用凭证
     */
    private List<ErpReimAsstDto> generateNoInvoiceFeeVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                                  List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成无发票费用凭证");
        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成往来支付凭证
     */
    private List<ErpReimAsstDto> generateTransactionVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                                 List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成往来支付凭证");
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 调用原有的往来支付逻辑
        // 这里可以重用原有的 saveTransactionsVcrAssts 方法的核心逻辑

        return insertAssts;
    }

    /**
     * 生成借款凭证
     */
    private List<ErpReimAsstDto> generateLoanVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                          List<ErpReimDetailVo> erpReimDetailVos) {
        log.info("生成借款凭证");
        return generateGeneralVoucherAssts(param, flowContext, erpReimDetailVos, FeeNameEnum.getByType(param.getType()).getFeeName());
    }

    /**
     * 生成通用凭证辅助项
     * 适用于大部分费用类型的通用逻辑
     */
    private List<ErpReimAsstDto> generateGeneralVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                             List<ErpReimDetailVo> erpReimDetailVos, String feeName) {
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 这里实现通用的凭证生成逻辑
        // 可以参考原有的 generalAsstGen 方法

        List<ErpReimAsstDto> reimAsstDetails = param.getReimAsstDetails();
        if (reimAsstDetails == null || reimAsstDetails.isEmpty()) {
            log.warn("未找到预置辅助项配置，使用默认逻辑");
            return insertAssts;
        }

        // 简化的通用逻辑示例
        int asstNo = flowContext.getNextAsstNo();
        for (ErpReimDetailVo reimDetail : erpReimDetailVos) {
            // 生成借方辅助项
            ErpReimAsstDto ysCR = reimAsstDetails.stream()
                    .filter(asst -> MedConst.TYPE_2.equals(asst.getActigSys()) && MedConst.TYPE_1.equals(asst.getActigAmtType()))
                    .findFirst().orElse(null);

            if (ysCR != null) {
                ErpReimAsstDto cr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysCR, cr);
                cr.setSupType(param.getSupType());
                cr.setActigAmt(reimDetail.getSum());
                cr.setReimDetailId(reimDetail.getId());
                cr.setAsstNo(asstNo++);
                cr.setAbst(feeName + "-报销");
                insertAssts.add(cr);
            }

            // 生成贷方辅助项
            ErpReimAsstDto ysDR = reimAsstDetails.stream()
                    .filter(asst -> MedConst.TYPE_2.equals(asst.getActigSys()) && MedConst.TYPE_2.equals(asst.getActigAmtType()))
                    .findFirst().orElse(null);

            if (ysDR != null) {
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysDR, dr);
                dr.setSupType(param.getSupType());
                dr.setReimDetailId(reimDetail.getId());
                dr.setAsstNo(asstNo++);
                dr.setActigAmt(reimDetail.getSum());
                dr.setAbst(feeName + "-报销");
                insertAssts.add(dr);
            }
        }

        // 更新上下文中的辅助项编号计数器
        flowContext.setAsstNoCounter(asstNo);

        return insertAssts;
    }


    /**
     * 生成通用凭证辅助项【模版配置】
     * 适用于大部分费用类型的通用逻辑
     */
    private List<ErpReimAsstDto> generateGeneralVcrAsstsByTemplate(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext,
                                                                   List<ErpReimDetailVo> erpReimDetailVos, String feeName) {
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        // 这里实现通用的凭证生成逻辑
        // 可以参考原有的 generalAsstGen 方法

        List<ErpReimAsstDto> reimAsstDetails = param.getReimAsstDetails();
        if (reimAsstDetails == null || reimAsstDetails.isEmpty()) {
            log.warn("未找到预置辅助项配置，使用默认逻辑");
            return insertAssts;
        }

        // 简化的通用逻辑示例
        int asstNo = flowContext.getNextAsstNo();
        for (ErpReimDetailVo reimDetail : erpReimDetailVos) {
            // 生成借方辅助项
            ErpReimAsstDto ysCR = reimAsstDetails.stream()
                    .filter(asst -> MedConst.TYPE_2.equals(asst.getActigSys()) && MedConst.TYPE_1.equals(asst.getActigAmtType()))
                    .findFirst().orElse(null);

            if (ysCR != null) {
                ErpReimAsstDto cr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysCR, cr);
                cr.setSupType(param.getSupType());
                cr.setActigAmt(reimDetail.getSum());
                cr.setReimDetailId(reimDetail.getId());
                cr.setAsstNo(asstNo++);
                cr.setAbst(feeName + "-报销");
                insertAssts.add(cr);
            }

            // 生成贷方辅助项
            ErpReimAsstDto ysDR = reimAsstDetails.stream()
                    .filter(asst -> MedConst.TYPE_2.equals(asst.getActigSys()) && MedConst.TYPE_2.equals(asst.getActigAmtType()))
                    .findFirst().orElse(null);

            if (ysDR != null) {
                ErpReimAsstDto dr = new ErpReimAsstDto();
                BeanUtils.copyProperties(ysDR, dr);
                dr.setSupType(param.getSupType());
                dr.setReimDetailId(reimDetail.getId());
                dr.setAsstNo(asstNo++);
                dr.setActigAmt(reimDetail.getSum());
                dr.setAbst(feeName + "-报销");
                insertAssts.add(dr);
            }
        }

        // 更新上下文中的辅助项编号计数器
        flowContext.setAsstNoCounter(asstNo);

        return insertAssts;
    }
} 