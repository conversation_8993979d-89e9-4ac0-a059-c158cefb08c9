<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.read.CmsContractReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractManage.vo.CmsContractVo" id="contractMap">
        <result property="id" column="id" />
        <result property="ctCode" column="ct_code" />
        <result property="ctName" column="ct_name" />
        <result property="tenderNum" column="tender_num" />
        <result property="tenderName" column="tender_name" />
        <result property="bidWinningTime" column="bid_winning_time" />
        <result property="signTime" column="sign_time" />
        <result property="useOrg" column="use_org" />
        <result property="oppositeName" column="opposite_name" />
        <result property="oppositeId" column="opposite_id" />
        <result property="mainId" column="main_id" />
        <result property="ctTypeCode" column="ct_type_code" />
        <result property="ctStatus" column="ct_status" />
        <result property="responsiblePerson" column="responsible_person" />
        <result property="responsiblePhone" column="responsible_phone" />
        <result property="oppositeBank" column="opposite_bank" />
        <result property="oppositeAccount" column="opposite_account" />
        <result property="totalAmt" column="total_amt" />
        <result property="paymentType" column="payment_type" />
        <result property="paymentTerms" column="payment_terms" />
        <result property="auditBchno" column="audit_bchno" />
        <result property="appyer" column="appyer" />
        <result property="applyTime" column="apply_time" />
        <result property="appyOrgId" column="appy_org_id" />
        <result property="chkState" column="chk_state" />
        <result property="crter" column="crter" />
        <result property="createTime" column="create_time" />
        <result property="hospitalId" column="hospital_id" />
        <result property="isDeleted" column="is_deleted" />
        <result property="contractRemark" column="contract_remark" />
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractVo">
        SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
        ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
        bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg,
        co.use_org_person AS useOrgPerson, use_person_info.emp_name AS useOrgPersonName,
        cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, co.ct_type_code AS
        ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
        hei.emp_name AS responsiblePersonName, co.responsible_phone AS responsiblePhone,
        co.opposite_bank AS oppositeBank, co.opposite_account AS oppositeAccount, co.total_amt AS
        totalAmt, co.payment_type AS paymentType, co.payment_terms AS paymentTerms, co.audit_bchno
        AS auditBchno, co.appyer AS appyer, hei1.emp_name AS appyerName, co.apply_time AS applyTime,
        co.appy_org_id AS appyOrgId, co.chk_state AS chkState, co.crter AS crter, co.create_time AS
        createTime, co.hospital_id AS hospitalId, co.is_deleted AS isDeleted, co.contract_remark AS
        contractRemark, co.opposite_person AS oppositePerson, co.opposite_phone AS oppositePhone,
        co.data_str AS dataStr, co.att_name AS attName, co.att, co.show_key_element AS
        showKeyElement, co.show_pay_terms AS showPayTerms, co.chk_state1 AS chkState1,
        co.audit_bchno1 AS auditBchno1, co.appyer1 AS appyer1, co.apply_time1 AS applyTime1,
        co.validity_start_date AS validityStartDate, co.validity_end_date AS validityEndDate,
        co.total_amt_in_word AS totalAmtInWord, co.manage_org AS manageOrg, co.renewal AS renewal,
        co.last_contract_id AS lastContractId, co.invalid_flag AS invalidFlag, coc.contact_name AS
        contactName, coc.phone AS contactPhone, cof.opening_bank AS openingBank, cof.bank_account AS
        bankAccount, co.draft_type AS draftType, co.draft_process_instance_code AS
        draftProcessInstanceCode, co.signed_process_instance_code AS signedProcessInstanceCode ,
        co.opposite_person_id AS oppositePersonId , co.opposite_bank_id AS oppositeBankId,
        co.ct_copies as ctCopies, co.attachment_names as attachmentNames FROM cms_contract co LEFT
        JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code LEFT JOIN
        hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN
        hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0 LEFT JOIN cms_contract_opposite cco ON
        co.opposite_id = cco.id LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id =
        coc.id LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id <where>
        (co.invalid_flag = '0' or co.invalid_flag is null) <if
                test="chkState != null and chkState != '' "> and co.chk_state =
        #{chkState,jdbcType=VARCHAR} </if>
            <if test="chkState1 != null and chkState1 != '' "> and
        co.chk_state1 = #{chkState1,jdbcType=VARCHAR} </if>
            <if test="id != null and id != ''"> and
        co.id = #{id,jdbcType=INTEGER} </if>

        <if test="oppositeName !=null and oppositeName != ''">
        and co.opposite_name =#{oppositeName,jdbcType=VARCHAR} </if>

            <if
                test="auditBchno != null and auditBchno != ''"> and co.audit_bchno =
        #{auditBchno,jdbcType=VARCHAR} </if>
        <if
                test="appyer != null and appyer != ''"> and co.appyer = #{appyer,jdbcType=VARCHAR} </if>
        <if
                test="appyer1 != null and appyer1 !=''"> and co.appyer1 =
        #{appyer1,jdbcType=VARCHAR} </if>
        <!-- 🔥 简化查询逻辑：如果同时设置了crter和useOrgPerson，使用OR查询 -->
        <if test="crter != null and crter != '' and useOrgPerson != null and useOrgPerson != ''">
            and (co.crter = #{crter,jdbcType=VARCHAR} or co.use_org_person = #{useOrgPerson,jdbcType=VARCHAR})
        </if>
        <!-- 如果只设置了crter -->
        <if test="crter != null and crter != '' and (useOrgPerson == null or useOrgPerson == '')">
            and co.crter = #{crter,jdbcType=VARCHAR}
        </if>
        <!-- 如果只设置了useOrgPerson -->
        <if test="useOrgPerson != null and useOrgPerson != '' and (crter == null or crter == '')">
            and co.use_org_person = #{useOrgPerson,jdbcType=VARCHAR}
        </if>
            <if
                test="responsiblePerson != null and responsiblePerson != ''"> and
        co.responsible_person = #{responsiblePerson,jdbcType=VARCHAR} </if>
            <if
                test="auditBchno1 != null and auditBchno1 != ''"> and co.audit_bchno1 =
        #{auditBchno1,jdbcType=VARCHAR} </if>
            <if test="ctName != null and ctName != ''"> and
        co.ct_name like CONCAT('%',#{ctName,jdbcType=VARCHAR},'%') </if>
        <if
                test="ctCode != null and ctCode != ''"> and co.ct_code like
        CONCAT('%',#{ctCode,jdbcType=VARCHAR},'%') </if>
        <if
                test="ctUnifiedCode != null and ctUnifiedCode != ''"> and co.ct_unified_code like
        CONCAT('%',#{ctUnifiedCode,jdbcType=VARCHAR},'%') </if>
            <if
                test="tenderNum != null and tenderNum != ''"> and co.tender_num =
        #{tenderNum,jdbcType=VARCHAR} </if>
            <if test="tenderName != null and tenderName != ''"> and
        co.tender_name = #{tenderName,jdbcType=VARCHAR} </if>
            <if
                test="bidWinningTime != null and bidWinningTime != ''"> and co.bid_winning_time =
        #{bidWinningTime,jdbcType=VARCHAR} </if>
            <if test="signTime != null and signTime != ''"> and
        co.sign_time = #{signTime,jdbcType=VARCHAR} </if>
            <if test="useOrg != null and useOrg != ''">
        and co.use_org = #{useOrg,jdbcType=VARCHAR} </if>
            <if
                test="startTime != null and startTime != ''"> and (co.apply_time between
        #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} or co.apply_time is null or
        co.apply_time = 'null') </if>
            <if test="paymentType != null and paymentType != ''"> and
        co.payment_type = #{paymentType,jdbcType=VARCHAR} </if>
        </where>
    </select>

    <select id="checkBchnoWhere" resultType="int"> SELECT COUNT(*) FROM cms_contract <where>
            <if test="auditBchno1 != null and auditBchno1 != ''"> audit_bchno1 =
        #{auditBchno1,jdbcType=VARCHAR} </if>
        </where>
    </select>

    <select id="queryIDByBchno" resultType="integer"> SELECT id FROM cms_contract <where>
            <if test="bchno != null and bchno != ''"> audit_bchno1 = #{bchno,jdbcType=VARCHAR} or
        audit_bchno = #{bchno,jdbcType=VARCHAR} </if>
        </where>
    </select>

    <!-- 查询各分类已归档合同数量 -->
    <select id="queryContractTypeCount"
        resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractVo"> SELECT ct.ct_type_code
        as ctTypeCode, t.name as typeName, COUNT(ct.id) as contractCount, ct.hospital_id as
        hospitalId FROM cms_contract ct LEFT JOIN cms_contract_type t ON ct.ct_type_code = t.code
        WHERE ct.is_deleted != 1 AND ct.chk_state = '1' AND ct.chk_state1 = '1' <if
            test="hospitalId != null and hospitalId != ''"> AND ct.hospital_id = #{hospitalId} </if>
        GROUP BY ct.ct_type_code, t.name, ct.hospital_id ORDER BY ct.ct_type_code </select>
</mapper>