package com.jp.med.erp.modules.liteflow.component.drug;

import com.jp.med.erp.modules.liteflow.param.VoucherFlowParam;
import com.jp.med.erp.modules.liteflow.vo.ExpenseVcrFlowContextVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 药品报销凭证生成组件
 * 专门处理药品报销的复杂逻辑
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "DrugVoucherGenerateComponent", name = "药品报销凭证生成")
public class DrugVoucherGenerateComponent extends NodeComponent {

    @Override
    public void process() throws Exception {
        log.info("开始执行药品报销凭证生成节点");

        // 获取流程参数
        VoucherFlowParam param = this.getRequestData();
        // 获取上下文
        ExpenseVcrFlowContextVo flowContext = this.getContextBean(ExpenseVcrFlowContextVo.class);

        try {
//            List<ErpReimAsstDto> insertAssts = generateDrugVoucherAssts(param, flowContext);
//
//            // 设置创建人员信息
//            insertAssts.forEach(asst -> {
//                asst.setCrter(param.getRecordPersonId());
//                asst.setCreateTime(DateUtil.getCurrentTime(null));
//                asst.setHospitalId(param.getHospitalId());
//            });
//
//            // 添加到上下文
//            flowContext.addGeneratedAssts(insertAssts);
//
//            log.info("药品报销凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());

        } catch (Exception e) {
            log.error("药品报销凭证生成失败", e);
            flowContext.markFlowFailed("药品报销凭证生成失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 生成药品报销凭证辅助项
     * 简化版本 - 生成基本的药品报销凭证
     */
    private List<ErpReimAsstDto> generateDrugVoucherAssts(VoucherFlowParam param, ExpenseVcrFlowContextVo flowContext) {
        log.info("------------------start生成药品报销assts-------------------------");
        
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();
        int asstNo = flowContext.getNextAsstNo();
        
        // 简化的药品报销凭证生成逻辑
        // 实际使用时需要根据具体的业务需求和数据结构来完善
        
        for (Integer reimId : param.getIds()) {
            // 生成财务借方（药品费用）
            ErpReimAsstDto cwCR = new ErpReimAsstDto();
            cwCR.setSupType(param.getSupType());
            cwCR.setActigSys(MedConst.TYPE_1); // 财务会计
            cwCR.setActigAmtType(MedConst.TYPE_1); // 借方
            cwCR.setActigSubCode("110220100");
            cwCR.setActigSubName("库存药品");
            cwCR.setActigAmt(new BigDecimal("1000")); // 示例金额，实际需要从数据库查询
            cwCR.setReimDetailId(reimId);
            cwCR.setAsstNo(asstNo++);
            cwCR.setAbst("药品采购入库");
            insertAssts.add(cwCR);
            
            // 生成财务贷方（应付账款）
            ErpReimAsstDto cwDR = new ErpReimAsstDto();
            cwDR.setSupType(param.getSupType());
            cwDR.setActigSys(MedConst.TYPE_1); // 财务会计
            cwDR.setActigAmtType(MedConst.TYPE_2); // 贷方
            cwDR.setActigSubCode("220210100");
            cwDR.setActigSubName("应付账款");
            cwDR.setActigAmt(new BigDecimal("1000")); // 示例金额
            cwDR.setReimDetailId(reimId);
            cwDR.setAsstNo(asstNo++);
            cwDR.setAbst("药品采购入库");
            insertAssts.add(cwDR);
            
            // 生成预算借方（事业支出）
            ErpReimAsstDto ysCR = new ErpReimAsstDto();
            ysCR.setSupType(param.getSupType());
            ysCR.setActigSys(MedConst.TYPE_2); // 预算会计
            ysCR.setActigAmtType(MedConst.TYPE_1); // 借方
            ysCR.setActigSubCode("503020101");
            ysCR.setActigSubName("药品费");
            ysCR.setEconSubCode("302180601");
            ysCR.setEconSubName("西药");
            ysCR.setActigAmt(new BigDecimal("1000")); // 示例金额
            ysCR.setReimDetailId(reimId);
            ysCR.setAsstNo(asstNo++);
            ysCR.setAbst("药品采购支出");
            insertAssts.add(ysCR);
            
            // 生成预算贷方（应付款项）
            ErpReimAsstDto ysDR = new ErpReimAsstDto();
            ysDR.setSupType(param.getSupType());
            ysDR.setActigSys(MedConst.TYPE_2); // 预算会计
            ysDR.setActigAmtType(MedConst.TYPE_2); // 贷方
            ysDR.setActigSubCode("602080201");
            ysDR.setActigSubName("预算应付账款");
            ysDR.setActigAmt(new BigDecimal("1000")); // 示例金额
            ysDR.setReimDetailId(reimId);
            ysDR.setAsstNo(asstNo++);
            ysDR.setAbst("药品采购支出");
            insertAssts.add(ysDR);
        }
        
        // 更新上下文中的辅助项编号计数器
        flowContext.setAsstNoCounter(asstNo);
        
        log.info("------------------end生成药品报销assts-------------------------");
        return insertAssts;
    }
} 