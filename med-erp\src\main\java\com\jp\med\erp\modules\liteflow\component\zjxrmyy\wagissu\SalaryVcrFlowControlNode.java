package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 工资凭证流程控制节点
 * 负责管理所有工资凭证组件的执行顺序、状态管理和错误处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrFlowControlNode", name = "工资凭证流程控制")
public class SalaryVcrFlowControlNode extends NodeComponent {

    @Override
    public void process() throws Exception {
        log.info("开始执行工资凭证流程控制节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        try {
            // 初始化流程状态
            initializeFlowState(flowContext, baseParamDtos);

            // 验证必要条件
            validatePreConditions(flowContext, baseParamDtos);

            // 记录流程开始
            logFlowStart(flowContext, baseParamDtos);

            log.info("工资凭证流程控制节点执行完成");

        } catch (Exception e) {
            log.error("工资凭证流程控制失败", e);
            flowContext.markFlowFailed("工资凭证流程控制失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 初始化流程状态
     */
    private void initializeFlowState(SalaryVcrFlowContextVo flowContext, SalaryVcrBaseParamVo baseParamDtos) {
        log.info("初始化工资凭证流程状态，任务ID: {}", baseParamDtos.getTaskId());

        // 设置流程基本信息
        flowContext.setTaskId(baseParamDtos.getTaskId());

        // 初始化辅助项编号（如果需要的话）
        // flowContext.setCurrentAsstNo(1);

        log.info("工资凭证流程状态初始化完成");
    }

    /**
     * 验证执行必要条件
     */
    private void validatePreConditions(SalaryVcrFlowContextVo flowContext, SalaryVcrBaseParamVo baseParamDtos) {
        log.info("验证工资凭证流程执行条件");

        // 验证任务ID
        if (baseParamDtos.getTaskId() == null) {
            throw new RuntimeException("任务ID不能为空");
        }

        // 验证任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails();
        if (taskDetails == null || taskDetails.isEmpty()) {
            throw new RuntimeException("工资任务详情数据不能为空");
        }

        // 验证凭证模版配置
        if (baseParamDtos.getVoucheTmeplateDtoMap() == null || baseParamDtos.getVoucheTmeplateDtoMap().isEmpty()) {
            throw new RuntimeException("凭证模版配置数据不能为空");
        }

        // 验证业务凭证类型配置
        validateVoucherTemplateTypes(baseParamDtos);

        // 验证任务详情数据完整性
        validateTaskDetails(taskDetails);

        log.info("工资凭证流程执行条件验证通过");
    }

    /**
     * 验证凭证模版类型配置
     */
    private void validateVoucherTemplateTypes(SalaryVcrBaseParamVo baseParamDtos) {
        // 检查关键业务凭证类型是否配置
        String[] requiredTypes = {
                ErpConstants.BUSIVOUCHERTYPE_ACCRUE_WAG,      // 工资计提
                ErpConstants.BUSIVOUCHERTYPE_ACCRUE_ENTP_INSUR_GOLD, // 四险两金企业缴纳
                ErpConstants.BUSIVOUCHERTYPE_PAYROLL_PAYABLE,  // 应付职工薪酬
                ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_IIT,  // 个人计提个人所得税
                ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI,  // 个人计提三险两金
                ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_UNION,  // 个人计提工会会费
                ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_TEMP_DEDUCT,  // 个人计提临时扣款
                ErpConstants.BUSIVOUCHERTYPE_SALARY_PAYROLL, //实际发放
                ErpConstants.BUSIVOUCHERTYPE_SALARY_BUDGET //预算凭证
        };

        for (String type : requiredTypes) {
            if (!baseParamDtos.getVoucheTmeplateDtoMap().containsKey(type) ||
                    baseParamDtos.getVoucheTmeplateDtoMap().get(type).isEmpty()) {
                log.warn("缺少业务凭证类型配置: {}", type);
            }
        }
    }

    /**
     * 验证任务详情数据
     */
    private void validateTaskDetails(List<SalaryVcrTaskDetialVo> taskDetails) {
        // 统计各类型任务数量
        long salaryCount = taskDetails.stream()
                .filter(detail -> ErpConstants.SALARY.equals(detail.getSalaryType()))
                .count();

        long businessPayCount = taskDetails.stream()
                .filter(detail -> ErpConstants.BUSINESS_PAYMENT.equals(detail.getSalaryType()))
                .count();

        long reduceCount = taskDetails.stream()
                .filter(detail -> ErpConstants.INDIVDUAL_REDUCE.equals(detail.getSalaryType()))
                .count();

        log.info("任务详情统计: 工资计提={}, 企业缴纳={}, 应付职工薪酬={}, 个人代扣={}",
                salaryCount, businessPayCount, reduceCount);

        // 验证数据完整性
        if (salaryCount == 0 && businessPayCount == 0 && reduceCount == 0) {
            throw new RuntimeException("没有有效的工资任务详情数据");
        }

        // 验证金额数据
        BigDecimal totalAmount = taskDetails.stream()
                .map(SalaryVcrTaskDetialVo::getReimAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("工资任务详情总金额为0或负数: {}", totalAmount);
        }
    }

    /**
     * 记录流程开始日志
     */
    private void logFlowStart(SalaryVcrFlowContextVo flowContext, SalaryVcrBaseParamVo baseParamDtos) {
        log.info("================== 工资凭证生成流程开始 ==================");
        log.info("任务ID: {}", baseParamDtos.getTaskId());
        log.info("任务详情数量: {}", flowContext.getVcrTaskDetails().size());
        log.info("凭证模版类型数量: {}", baseParamDtos.getVoucheTmeplateDtoMap().size());
        log.info("=========================================================");
    }
} 