
        <where>
            <if test="reimFlag != null and reimFlag != ''">
                and reim_flag = #{reimFlag,jdbcType=VARCHAR}
            </if>
            <if test="itemName != null and itemName != ''">
                and exists (select 1
                            from ecs_reim_purc_task_detail m
                            where m.task_id = a.id
                              and POSITION(m.reim_desc IN #{itemName,jdbcType=VARCHAR}) > 0)
            </if>
            <if test="reimTaskType != null and reimTaskType != ''">
                and a.reim_task_type = #{reimTaskType,jdbcType=VARCHAR}
            </if>
            <if test="exeEmpCode != null and exeEmpCode != ''">
                and a.exe_emp_code = #{exeEmpCode,jdbcType=VARCHAR}
            </if>
            <!-- 需要添加这个条件 -->
            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
        </where>
