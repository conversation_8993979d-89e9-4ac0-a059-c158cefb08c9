package com.jp.med.erp.modules.liteflow.vo;

import com.jp.med.common.vo.EmployeeReallySalaryVo;
import com.jp.med.erp.modules.config.dto.ErpReimSalaryTaskDetailDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryRelCoConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工资凭证流程上下文
 * 在LiteFlow流程组件间传递数据
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class SalaryVcrFlowContextVo extends VcrFlowContextBaseVo {

    /**
     * 工资任务明细列表
     */
    private List<ErpReimSalaryTaskDetailDto> taskDetails;

    /**
     * 工资凭证明细列表
     */
    private List<SalaryVcrTaskDetialVo> vcrTaskDetails;

    /**
     * 工资类型
     */
    private String salaryType;

    /**
     * 应付职工薪酬 - 对应明细数据
     */
    List<SalaryVcrTaskDetialVo> payrollPayableDetailList = new ArrayList<>();

    /**
     * 特殊往来账 - 对应明细数据
     */
    List<SalaryVcrTaskDetialVo> specialAccountsDetailList = new ArrayList<>();

    /**
     * 冲账凭证生成 - 对应明细数据
     */
    List<SalaryVcrTaskDetialVo> strikeBalanceDetailList = new ArrayList<>();

    /**
     * 累计扣款凭证生成 - 对应明细数据
     */
    List<SalaryVcrTaskDetialVo> accumulatedDeductionsDetailList = new ArrayList<>();

    /**
     * 银行存款凭证生成 - 对应明细数据
     */
    List<SalaryVcrTaskDetialVo> bankDepositDetailList = new ArrayList<>();

    /**
     * 职工应发工资明细
     */
    List<EmployeeReallySalaryVo> employeeReallySalaryVoList;

} 