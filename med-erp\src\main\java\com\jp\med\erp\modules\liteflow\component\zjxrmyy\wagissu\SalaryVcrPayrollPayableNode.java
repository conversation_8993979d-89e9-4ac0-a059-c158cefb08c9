package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.constant.MedConst;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.jp.med.erp.modules.vcrGen.mapper.read.ErpVcrDetailReadMapper;
import com.jp.med.erp.modules.vcrGen.vo.ErpReimAsstVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 应付职工薪酬凭证生成节点
 * 处理基本工资、津贴补贴、绩效工资等应付职工薪酬相关的凭证生成
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrPayrollPayableNode", name = "应付职工薪酬凭证生成")
public class SalaryVcrPayrollPayableNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Autowired
    private ErpVcrDetailReadMapper erpVcrDetailReadMapper;

    @Override
    public void process() throws Exception {
        log.info("开始执行应付职工薪酬凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);

        // 获取个人代扣任务详情
        List<SalaryVcrTaskDetialVo> taskDetails = flowContext.getVcrTaskDetails()
                .stream()
                .filter(detail -> StringUtils.equals(detail.getSalaryType(), ErpConstants.INDIVDUAL_REDUCE))
                .collect(Collectors.toList());

        if (taskDetails.isEmpty()) {
            log.info("没有个人代扣任务明细，跳过应付职工薪酬处理");
            return;
        }

        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理应付职工薪酬相关凭证
            insertAssts.addAll(processPayrollPayable(baseParamDtos, flowContext));

        } catch (Exception e) {
            log.error("应付职工薪酬凭证生成失败", e);
            flowContext.markFlowFailed("应付职工薪酬凭证生成失败: " + e.getMessage());
            throw e;
        }

        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);

        log.info("应付职工薪酬凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理应付职工薪酬相关凭证
     */
    private List<ErpReimAsstDto> processPayrollPayable(SalaryVcrBaseParamVo baseParamDtos,
                                                       SalaryVcrFlowContextVo flowContext) {
        log.info("开始处理应付职工薪酬");

        List<ErpReimAsstDto> result = new ArrayList<>();
        List<VoucherTemplateDto> templates = baseParamDtos.getVoucheTmeplateDtoMap()
                .getOrDefault(ErpConstants.BUSIVOUCHERTYPE_PAYROLL_PAYABLE, new ArrayList<>());

        if (templates.isEmpty()) {
            log.warn("未找到应付职工薪酬凭证模版配置");
            return result;
        }

        //获取计提工资对应任务id
        Integer accrueTaskId = baseParamDtos.getSalaryTaskMap().get(ErpConstants.SALARY);
        // 查询工资计提辅助项，获取应付职工薪酬科目金额
        ErpReimAsstDto asstParam = new ErpReimAsstDto();
        asstParam.setSupType(MedConst.TYPE_3);
        asstParam.setReimDetailId(accrueTaskId);
        List<ErpReimAsstVo> erpReimAsstVos = erpVcrDetailReadMapper.queryReimAsstVoList(asstParam);

        // 处理基本工资总额 (220101)
        processPayrollPayableBySubCode(templates, result, flowContext, erpReimAsstVos,
                "220101", ErpConstants.SALARY_TOTAL_BASE, "工资总额-应付职工薪酬-基本工资（含离退休费）");

        // 处理国家统一规定的津贴补贴 (220102)
        processPayrollPayableBySubCode(templates, result, flowContext, erpReimAsstVos,
                "220102", ErpConstants.SALARY_TOTAL_ALLOWANCE, "工资总额-应付职工薪酬-国家统一规定的津贴补贴");

        // 处理规范津贴补贴 (220103)
        processPayrollPayableBySubCode(templates, result, flowContext, erpReimAsstVos,
                "220103", ErpConstants.SALARY_TOTAL_PERFORM_SAL, "工资总额-应付职工薪酬-规范津贴补贴（绩效工资）");

        log.info("应付职工薪酬处理完成，生成 {} 个辅助项", result.size());
        return result;
    }

    /**
     * 按科目代码处理应付职工薪酬
     */
    private void processPayrollPayableBySubCode(List<VoucherTemplateDto> templates,
                                                List<ErpReimAsstDto> result,
                                                SalaryVcrFlowContextVo flowContext,
                                                List<ErpReimAsstVo> erpReimAsstVos,
                                                String subCode,
                                                String reimName,
                                                String description) {

        List<ErpReimAsstVo> targetAssts = erpReimAsstVos.stream()
                .filter(e -> StringUtils.equals(e.getActigSubCode(), subCode)
                        && StringUtils.equals(e.getActigSys(), MedConst.TYPE_1)
                        && StringUtils.equals(e.getActigAmtType(), MedConst.TYPE_2))
                .collect(Collectors.toList());

        BigDecimal total = targetAssts.stream()
                .map(ErpReimAsstVo::getActigAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (total.compareTo(BigDecimal.ZERO) > 0) {
            // 创建虚拟明细用于模版匹配
            SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(reimName, total, description);

            // 匹配模版并生成凭证
            Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
            if (matchedTemplate.isPresent()) {
                List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                        matchedTemplate.get(), virtualDetail, flowContext);
                // 设置应付职工薪酬的数据进入上下文
                flowContext.getPayrollPayableDetailList().add(virtualDetail);
                result.addAll(detailAssts);
                log.debug("应用模版 {} 生成 {} 个辅助项", matchedTemplate.get().getRuleName(), detailAssts.size());
            }
        }
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        return detail;
    }
} 