package com.jp.med.erp.modules.liteflow.vo;

import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryRelCoConfigVo;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class VcrFlowContextBaseVo {
    /**
     * 凭证业务分类
     * 1：报销
     * 2：药品
     * 3：工资
     * 4：折旧
     */
    private String supType;


    /**
     * 医疗机构编码
     */
    private String hospitalId;

    /**
     * 业务凭证类型
     */
    private String busiVoucherType;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 科室映射关系
     */
    private Map<String, HrmOrgAgencyMapVo> orgMappings;

    /**
     * 生成的凭证辅助项列表
     */
    private List<ErpReimAsstDto> generatedAssts = new ArrayList<>();

    /**
     * 流程执行状态
     */
    private SalaryVcrFlowContextVo.FlowExecutionStatus status = SalaryVcrFlowContextVo.FlowExecutionStatus.INIT;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 流程开始时间
     */
    private Long startTime;

    /**
     * 流程结束时间
     */
    private Long endTime;

    /**
     * 当前处理的任务明细索引
     */
    private Integer currentDetailIndex = 0;

    /**
     * 已处理的任务明细数量
     */
    private Integer processedDetailCount = 0;

    /**
     * 辅助项编号计数器
     */
    private Integer asstNoCounter = 1;

    /**
     * 添加生成的凭证辅助项
     *
     * @param assts 凭证辅助项列表
     */
    public void addGeneratedAssts(List<ErpReimAsstDto> assts) {
        if (assts != null && !assts.isEmpty()) {
            this.generatedAssts.addAll(assts);
        }
    }

    /**
     * 添加单个凭证辅助项
     *
     * @param asst 凭证辅助项
     */
    public void addGeneratedAsst(ErpReimAsstDto asst) {
        if (asst != null) {
            this.generatedAssts.add(asst);
        }
    }

    /**
     * 获取下一个辅助项编号
     *
     * @return 辅助项编号
     */
    public Integer getNextAsstNo() {
        return asstNoCounter++;
    }

    /**
     * 重置辅助项编号计数器
     */
    public void resetAsstNoCounter() {
        this.asstNoCounter = 1;
    }

    /**
     * 设置流程开始时间
     */
    public void markFlowStart() {
        this.startTime = System.currentTimeMillis();
        this.status = SalaryVcrFlowContextVo.FlowExecutionStatus.RUNNING;
    }

    /**
     * 设置流程结束时间
     */
    public void markFlowEnd() {
        this.endTime = System.currentTimeMillis();
        this.status = SalaryVcrFlowContextVo.FlowExecutionStatus.COMPLETED;
    }

    /**
     * 设置流程失败
     *
     * @param errorMessage 错误信息
     */
    public void markFlowFailed(String errorMessage) {
        this.endTime = System.currentTimeMillis();
        this.status = SalaryVcrFlowContextVo.FlowExecutionStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * 获取流程执行时长（毫秒）
     *
     * @return 执行时长
     */
    public Long getExecutionDuration() {
        if (startTime == null) {
            return 0L;
        }
        Long end = endTime != null ? endTime : System.currentTimeMillis();
        return end - startTime;
    }

    /**
     * 流程执行状态枚举
     */
    public enum FlowExecutionStatus {
        INIT,       // 初始化
        RUNNING,    // 运行中
        COMPLETED,  // 完成
        FAILED      // 失败
    }

    /**
     * 创建人
     */
    private String crter;

    /**
     * 员工往来单位对照Map,多条情况取第一条
     */
    Map<String, List<ErpVcrSalaryRelCoConfigVo>> erpVcrSalaryRelCoConfigMap = new HashMap<>();
}
