package com.jp.med.erp.modules.liteflow.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class VcrFlowDetailBaseVo {

    /**
     * 报销科室
     */
    private String orgId;
    /**
     * 科室名称
     */
    private String orgName;

    /**
     * 科室类型 1:临床医技医辅 2:行政
     */
    private String deptType;

    /**
     * 资金性质 001:财政补助 003:自有资金等
     */
    private String fundNature;

    /**
     * 险种类型 （社保相关）
     */
    private String insuranceType;

    /**
     * 科研归属
     */
    private String researchAttribution;

    /**
     * 启用年度 1:当年 0:以前年度
     */
    private String enableYear;

    /**
     * 业务凭证类型
     */
    private String busiVoucherType;

    /**
     * 工资类型 1:工资计提 2:个人代扣 3:企业缴纳
     */
    private String salaryType;

    /**
     * 员工编号
     */
    private String empCode;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 经济科目编码
     */
    private String econSubCode;

    /**
     * 经济科目名称
     */
    private String econSubName;

    /**
     * 人员类型
     */
    private String empType;

    /**
     * 报销金额
     */
    private BigDecimal reimAmt;

    /**
     * 报销摘要
     */
    private String reimDesc;

    /**
     * 报销项目
     */
    private String reimName;

    /**
     * 人员类型desc
     */
    private String empTypeDesc;
}
