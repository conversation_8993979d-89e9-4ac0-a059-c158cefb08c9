package com.jp.med.erp.modules.vcrGen.entity;


import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:凭证申请信息
 */
@Data
public class Certificate extends CommonQueryDto {
    /**
     * 会计主管
     */
    private String accountingSupervisor;

    /**
     * 凭证附件张数
     */
    private Integer attachAmount;

    /**
     * 凭证摘要
     */
    private String certificateAbs;

    /**
     * 凭证日期
     */
    private String certificateDate;

    /**
     * 机构代码
     */
    private String organCode;

    /**
     * 机构名称
     */
    private String organName;

    /**
     * 录入人id
     */
    private String recordPersonId;

    /**
     * 录入人名字
     */
    private String recordPersonName;

    /**
     * 凭证号id
     */
    private String idpzh;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 凭证内容明细
     */
    private List<CertificateDetail> certificateDetail;

    /**
     * 报销id
     */
    private List<Integer> ids;

    /**
     * id
     */
    private Integer id;

    /**
     * 金额
     */
    private BigDecimal actigAmt;

    /**
     * 凭证类型
     */
    private String type;

    /**
     * 是否根据部门动态生成财务科目
     */
    private String acdDept;

    /**
     * 辅助项
     */
    private List<ErpReimAsstDto> reimAsstDetails;

    /**
     * 工资类型类型
     **/
    private String salaryType;

    /**
     * 分摊类型
     **/
    private String shareType;

    /**
     * 上级类型
     **/
    private String supType;

    /**
     * 是否冲抵借款(冲抵借款不需要上传付款证明文件)
     */
    private String isLoan;

    /**
     * 付款方式 0 非现金  1：现金  可拓展
     **/
    private String payMethod;

    /**
     * 凭证号
     */
    private String vpzh;

    /**
     * 附件
     */
    private List<MultipartFile> attFiles;

}
