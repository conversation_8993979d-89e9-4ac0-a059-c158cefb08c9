package com.jp.med.erp.modules.vcrGen.vo;

import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.entity.CertificateDetail;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class ExpenseFlowParam {

    /**
     * 医疗机构编码
     */
    private String hospitalId;

    /**
     * 会计主管
     */
    private String accountingSupervisor;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 凭证日期
     */
    private String certificateDate;

    /**
     * 报销id
     */
    private List<Integer> ids;

    /**
     * 工资类型类型
     **/
    private String salaryType;

    /**
     * 分摊类型
     **/
    private String shareType;

    /**
     * 上级类型
     **/
    private String supType;

    /**
     * 凭证类型 费用报销: 1-差旅 2-培训 3-其他费用 4-分摊费用 6-合同 8-采购 10-物资采购 11-其他费用(无发票) 12-往来支付 13-借款
     */
    private String type;

    /**
     * 是否冲抵借款(冲抵借款不需要上传付款证明文件)
     */
    private String isLoan;

    /**
     * 付款方式 0 非现金  1：现金  可拓展
     **/
    private String payMethod;

    /**
     * 凭证号
     */
    private String vpzh;

}
