package com.jp.med.erp.modules.liteflow.component.zjxrmyy.wagissu;

import com.jp.med.common.vo.EmployeeReallySalaryVo;
import com.jp.med.erp.modules.liteflow.util.VoucherTemplateEngine;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrBaseParamVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrFlowContextVo;
import com.jp.med.erp.modules.liteflow.vo.SalaryVcrTaskDetialVo;
import com.jp.med.erp.modules.vcrGen.constant.ErpConstants;
import com.jp.med.erp.modules.vcrGen.dto.ErpReimAsstDto;
import com.jp.med.erp.modules.vcrGen.dto.VoucherTemplateDto;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特殊往来账凭证生成节点
 * 就业见习特殊往来账
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@LiteflowComponent(value = "SalaryVcrSpecialAccountEmpInshipNode", name = "就业见习特殊往来账凭证生成")
public class SalaryVcrSpecialAccountEmpInshipNode extends NodeComponent {

    @Resource
    VoucherTemplateEngine voucherTemplateEngine;

    @Override
    public void process() throws Exception {
        log.info("开始执行就业见习特殊往来账凭证生成节点");

        // 1.获取业务参数
        SalaryVcrBaseParamVo baseParamDtos = this.getRequestData();
        // 获取上下文
        SalaryVcrFlowContextVo flowContext = this.getContextBean(SalaryVcrFlowContextVo.class);
        //需要入库的凭证
        List<ErpReimAsstDto> insertAssts = new ArrayList<>();

        try {
            // 处理特殊往来账
            insertAssts.addAll(processSpecialAccounts(baseParamDtos, flowContext));
        } catch (Exception e) {
            log.error("就业见习特殊往来账凭证生成失败", e);
            flowContext.markFlowFailed("就业见习特殊往来账凭证生成失败: " + e.getMessage());
            throw e;
        }
        // 更新上下文中的辅助项
        flowContext.addGeneratedAssts(insertAssts);
        log.info("就业见习特殊往来账凭证生成节点执行完成，共生成 {} 个辅助项", insertAssts.size());
    }

    /**
     * 处理特殊往来账
     */
    private List<ErpReimAsstDto> processSpecialAccounts(SalaryVcrBaseParamVo baseParamDtos,
                                                        SalaryVcrFlowContextVo flowContext) {
        log.info("开始处理就业见习特殊往来账");

        List<ErpReimAsstDto> result = new ArrayList<>();

        try {
            // 获取在职职工工资明细数据,通过工资任务批次id查询应发明细
            List<EmployeeReallySalaryVo> salaryResult = flowContext.getEmployeeReallySalaryVoList();
            if (salaryResult == null || salaryResult.size() == 0) {
                log.warn("无法获取在职职工工资明细数据");
                return result;
            }
            // 处理就业见习特殊往来账
            result.addAll(processStaff(baseParamDtos, flowContext, salaryResult));

        } catch (Exception e) {
            log.error("处理特殊往来账失败", e);
        }

        log.info("特殊往来账处理完成，生成 {} 个辅助项", result.size());
        return result;
    }

    /**
     * 就业见习特殊往来账
     *
     * @param baseParamDtos
     * @param flowContext
     * @param salaryData
     * @return
     */
    private List<ErpReimAsstDto> processStaff(SalaryVcrBaseParamVo baseParamDtos,
                                              SalaryVcrFlowContextVo flowContext,
                                              List<EmployeeReallySalaryVo> salaryData) {
        // 过滤就业见习相关人员 , EmpType = 就业见习
        List<EmployeeReallySalaryVo> maintStaff = salaryData.stream()
                .filter(e -> ErpConstants.EMP_TYPE_EMP_INSHIP_NAME.equals(e.getEmpType()))
                .collect(Collectors.toList());
        return processSpecialStaffByType(baseParamDtos, flowContext, maintStaff,
                ErpConstants.SPECIAL_ACCOUNT_EMP_INSHIP, "就业见习特殊往来账-特殊往来账");
    }

    /**
     * 按类型处理特殊人员
     */
    private List<ErpReimAsstDto> processSpecialStaffByType(SalaryVcrBaseParamVo baseParamDtos,
                                                           SalaryVcrFlowContextVo flowContext,
                                                           List<EmployeeReallySalaryVo> staffList,
                                                           String reimName,
                                                           String description) {
        List<ErpReimAsstDto> result = new ArrayList<>();

        if (staffList.isEmpty()) {
            return result;
        }

        List<VoucherTemplateDto> templates = getTemplatesByReimName(baseParamDtos, reimName);

        for (EmployeeReallySalaryVo staff : staffList) {
            if (staff.getRealPayTotal() != null && staff.getRealPayTotal().compareTo(BigDecimal.ZERO) > 0) {
                SalaryVcrTaskDetialVo virtualDetail = createVirtualDetail(reimName, staff.getRealPayTotal(), description);
                virtualDetail.setEmpCode(staff.getEmpCode());
                virtualDetail.setOrgId(staff.getOrgId());
                Optional<VoucherTemplateDto> matchedTemplate = voucherTemplateEngine.matchBestTemplate(templates, virtualDetail);
                if (matchedTemplate.isPresent()) {
                    List<ErpReimAsstDto> detailAssts = voucherTemplateEngine.applyTemplateToGenerateAssts(
                            matchedTemplate.get(), virtualDetail, flowContext);
                    // 设置特殊往来账的数据进入上下文,往来账list
                    flowContext.getSpecialAccountsDetailList().add(virtualDetail);
                    result.addAll(detailAssts);
                    log.debug("特殊人员 {} 应用模版生成 {} 个辅助项", staff.getEmpCode(), detailAssts.size());
                }
            }
        }

        return result;
    }

    /**
     * 根据项目名称获取模版
     */
    private List<VoucherTemplateDto> getTemplatesByReimName(SalaryVcrBaseParamVo baseParamDtos, String reimName) {
        // 映射项目名称到业务凭证类型
        String busiVoucherType = mapReimNameToBusiVoucherType(reimName);
        return baseParamDtos.getVoucheTmeplateDtoMap().getOrDefault(busiVoucherType, new ArrayList<>());
    }

    /**
     * 映射项目名称到业务凭证类型
     */
    private String mapReimNameToBusiVoucherType(String reimName) {
        Map<String, String> mapping = new HashMap<>();
        mapping.put(ErpConstants.SPECIAL_ACCOUNT_EMP_INSHIP, ErpConstants.BUSIVOUCHERTYPE_SPECIAL_ACCOUNT_EMPINSHIP);
        return mapping.getOrDefault(reimName, ErpConstants.BUSIVOUCHERTYPE_PROVI_PSN_SI);
    }

    /**
     * 创建虚拟明细用于模版匹配
     */
    private SalaryVcrTaskDetialVo createVirtualDetail(String reimName, BigDecimal amount, String description) {
        SalaryVcrTaskDetialVo detail = new SalaryVcrTaskDetialVo();
        detail.setReimName(reimName);
        detail.setReimAmt(amount);
        detail.setReimDesc(description);
        detail.setSalaryType(ErpConstants.INDIVDUAL_REDUCE);
        return detail;
    }
} 